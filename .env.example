SKIP_PREFLIGHT_CHECK=true

# REACT_APP_API_URL=https://stage-points-system.technorely.com/backend/api
# REACT_APP_SOCKET_URL=https://stage-points-system.technorely.com

REACT_APP_SOCKET_URL=http://localhost:3001

REACT_APP_GOOGLE_PLACES_API=AIzaSyA1LGZyLBn2mZZgxQxuvU1vkevcHDIm96U

REACT_APP_TRACK_JS_TOKEN=476fd1e32d67479e8015c712dc7c41c9
REACT_APP_TRACK_JS_APP=template

PORT=3000

REACT_APP_BUY_TOKEN_URL=https://pancakeswap.finance/swap?chain=bscTestnet&inputCurrency=******************************************&outputCurrency=******************************************

# WEB3_PROVIDER=http://127.0.0.1:8545
REACT_APP_RPC_PROVIDER=https://bnb-testnet.g.alchemy.com/v2/********************************
REACT_APP_ETHERSCAN_API=https://api-testnet.bscscan.com/api
REACT_APP_IS_MAINNET=false

REACT_APP_BLOCKPASS_ID=1

REACT_APP_POLYGON_API_URL_SH_ID=http://*************:3001/v1/

REACT_APP_ADMIN_DID=did:polygonid:polygon:mumbai:2qCr1op51CZDuVuiWqvvtpkH2stL1eZJ4BTJBgwuiG

REACT_APP_VERIFI_API_POLYGON=http://*************:3030

REACT_APP_BSCSCAN_APIKEY=**********************************

REACT_APP_AUTH0_DOMAIN=dev-xeh6ukesqa0e1h0s.us.auth0.com
REACT_APP_AUTH0_CLIENT_ID=v8EJZE39d54Xy24ksjgwbmAGvdMLI1UX
REACT_APP_AUTH0_AUDIENCE=https://point-system
REACT_APP_AUTH0_SCOPE=openid profile email offline_access

REACT_APP_TESTING_UI_PRIVATE_KEY=string
REACT_APP_TESTING_UI_RPC_URL=string
REACT_APP_WALLET_CONNECT_PROJECT_ID=10a65fdcb4709b04e2d48a5b4df9627c
REACT_APP_TESTING_UI_PRIVATE_KEY=****************************************************************
REACT_APP_TESTING_UI_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545

GENERATE_SOURCEMAP=false

REACT_APP_PANCAKE_SWAP_TOKENS_URL='https://tokens.pancakeswap.finance/pancakeswap-default.json'

# TESTING_UI
REACT_APP_TESTING_UI=true
REACT_APP_TESTING_UI_PRIVATE_KEY=****************************************************************
REACT_APP_TESTING_UI_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545

REACT_APP_BLOCKPASS_CLIENT_ID=technorely_a350f
