{"name": "react-ts-template", "version": "0.1.0", "private": true, "engines": {"npm": ">=9.5.1", "node": ">=18.16.0"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@devexpress/dx-react-core": "^4.0.5", "@devexpress/dx-react-scheduler": "^4.0.5", "@devexpress/dx-react-scheduler-material-ui": "^4.0.5", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.13.7", "@mui/material": "^5.13.7", "@mui/styled-engine-sc": "^5.12.0", "@mui/x-date-pickers": "^5.0.20", "@tanstack/react-query": "^5.83.0", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@wagmi/core": "^2.18.0", "@web3modal/wagmi": "^5.0.2", "axios": "^0.21.1", "chart.js": "^4.3.3", "classnames": "^2.5.1", "dayjs": "^1.11.13", "dom-to-image": "^2.6.0", "downshift": "^7.6.0", "ethers": "^5.7.1", "faker": "^6.6.6", "file-saver": "^2.0.5", "final-form": "4.20.9", "history": "4.9.0", "html2canvas": "^1.4.1", "html2pdf": "0.0.11", "i18n-iso-countries": "^7.14.0", "i18next": "23.2.7", "i18next-browser-languagedetector": "7.0.1", "i18next-xhr-backend": "2.0.1", "jspdf": "^2.5.1", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lottie-react": "^2.4.0", "moment": "^2.30.1", "number-to-cyrillic": "^2.7.0", "query-string": "^8.1.0", "rc-pagination": "^3.5.0", "react": "^18.2.0", "react-big-calendar": "^1.8.1", "react-calendar": "^4.3.0", "react-chartjs-2": "^5.2.0", "react-collapsible": "^2.10.0", "react-datepicker": "^4.15.0", "react-dom": "^18.2.0", "react-final-form": "6.5.9", "react-google-places-autocomplete": "^4.0.1", "react-i18next": "13.0.1", "react-image-crop": "^10.1.5", "react-infinite-scroll-component": "^6.1.0", "react-phone-input-2": "^2.15.1", "react-qr-code": "2.0.17", "react-redux": "^7.2.1", "react-router-dom": "^5.0.0", "react-scripts": "^5.0.1", "react-select": "^5.7.3", "react-table": "^7.8.0", "react-tooltip": "^5.18.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.9", "redux": "^4.2.1", "redux-devtools-extension": "2.13.8", "redux-persist": "^6.0.0", "redux-thunk": "2.4.2", "socket.io-client": "^4.8.1", "styled-components": "^5.3.6", "swiper": "^10.0.3", "swr": "^2.2.0", "toastr": "^2.1.4", "trackjs": "3.10.1", "trim-canvas": "^0.1.2", "viem": "^2.33.1", "wagmi": "^2.16.0", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "devDependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@faker-js/faker": "^8.0.2", "@types/classnames": "^2.3.0", "@types/debounce-promise": "^3.1.6", "@types/file-saver": "^2.0.5", "@types/google.maps": "^3.53.4", "@types/history": "4.7.11", "@types/jest": "29.5.2", "@types/jwt-decode": "^2.2.1", "@types/lodash": "^4.14.195", "@types/moment-timezone": "^ 0.5.13", "@types/node": "20.4.1", "@types/numeral": "2.0.2", "@types/react": "^18.0.20", "@types/react-big-calendar": "^1.6.4", "@types/react-calendar": "^3.9.0", "@types/react-datepicker": "^4.11.2", "@types/react-dom": "^18.0.6", "@types/react-image-crop": "^8.1.3", "@types/react-redux": "^7.1.25", "@types/react-router-dom": "4.3.2", "@types/react-table": "^7.7.14", "@types/react-tooltip": "^3.11.0", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@types/reactstrap": "^8.7.1", "@types/toastr": "^2.1.40", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "eslint": "^8.44.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.0", "lint-staged": "8.1.5", "prettier": "1.17.0", "sass": "^1.90.0", "typescript": "^4.9.5"}, "overrides": {"@walletconnect/ethereum-provider": "2.13.0"}, "scripts": {"dev": "BROWSER=none react-scripts start", "dev:win": "set BROWSER=none && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "precommit": "lint-staged", "eslint": "eslint --fix **/*.{js,ts,tsx}", "typecheck": "tsc -p tsconfig.json", "prepare": "husky install"}, "eslintConfig": {"extends": "react-app"}, "browserslist": ["defaults", ">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "lint-staged": {"*.{ts,tsx}": ["eslint", "prettier --write", "git add"]}, "resolutions": {"node-gyp-build": "4.8.1"}, "sideEffects": false}