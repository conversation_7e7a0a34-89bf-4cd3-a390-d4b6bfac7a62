import React from 'react'
import { ENTERPRENEURS } from './App.constants'

const TimeOffRequests = React.lazy(() => import('screens/client/TimeOffRequests'))
const NewRequestTimeOff = React.lazy(() => import('screens/client/NewRequestTimeOff'))
const Goods = React.lazy(() => import('screens/client/Goods'))
const Information = React.lazy(() => import('screens/client/Information'))
const CryptoBot = React.lazy(() => import('screens/client/CryptoBot'))
const Policy = React.lazy(() => import('screens/client/Policy'))
const PolicyAdmin = React.lazy(() => import('screens/admin/PolicyAdmin'))
const Profile = React.lazy(() => import('screens/client/Profile'))
const UserRating = React.lazy(() => import('screens/client/Profile/components/UserRating'))
const Achievements = React.lazy(() => import('screens/client/Achievements'))
const Users = React.lazy(() => import('screens/admin/Users'))
const Subcontract = React.lazy(() => import('screens/client/Subcontract'))
const SubcontractTasks = React.lazy(() => import('screens/admin/SubcontractTasks'))
const Shop = React.lazy(() => import('screens/admin/Shop'))
const GoodsItem = React.lazy(() => import('screens/admin/GoodsItem'))
const GoodsItemDetails = React.lazy(() => import('screens/client/GoodsItemDetails'))
const AchievementsAdm = React.lazy(() => import('screens/admin/AchievementsAdm'))
const AchievementItem = React.lazy(() =>
  import('screens/admin/AchievementsAdm/components/AchievementItem')
)
const AchievementsItemDetails = React.lazy(() => import('screens/client/AchievementsItemDetails'))
const Requests = React.lazy(() => import('screens/admin/Requests'))
const Team = React.lazy(() => import('screens/client/Team'))
const TeamAdmin = React.lazy(() => import('screens/admin/Team'))
const Projects = React.lazy(() => import('screens/admin/Projects'))
const Project = React.lazy(() => import('screens/admin/Projects/components/Project'))
const Vacancies = React.lazy(() => import('screens/admin/Vacancies'))
const Vacancy = React.lazy(() => import('screens/admin/Vacancies/Vacancy'))
const CreateVacancy = React.lazy(() => import('screens/admin/Vacancies/CreateVacancy'))
const InvoiceModal = React.lazy(() => import('components/ProjectModals/InvoiceModal'))
const EditProfile = React.lazy(() => import('screens/client/EditProfile'))
const ForeignProfile = React.lazy(() => import('screens/client/ForeignProfile'))
const Basket = React.lazy(() => import('screens/client/Basket'))
const Settings = React.lazy(() => import('screens/admin/Settings'))
const Task = React.lazy(() => import('screens/client/Subcontract/components/Task'))
const TaskRequest = React.lazy(() => import('screens/client/TaskRequest'))
const CreateTask = React.lazy(() => import('screens/client/Subcontract/components/CreateTask'))
const OfferTasks = React.lazy(() => import('components/Subcontract/OfferTasks'))
const TaskPayment = React.lazy(() => import('screens/client/TaskPayment'))
const InterviewInvitation = React.lazy(() => import('screens/client/InterviewInvitation'))
const PlaningCalendar = React.lazy(() =>
  import('screens/client/Subcontract/components/PlaningCalendar')
)
const ProjectHistory = React.lazy(() => import('screens/admin/Projects/components/ProjectHistory'))
const Entrepreneurs = React.lazy(() => import('screens/admin/Entrepreneurs'))
const Verification = React.lazy(() => import('screens/client/Subcontract/components/Verification'))
const UserVerifiInfo = React.lazy(() =>
  import('screens/admin/SubcontractTasks/Verification/UserVerifiInfo')
)
const VerificationAdmin = React.lazy(() => import('screens/admin/SubcontractTasks/Verification'))
const CreateInvoice = React.lazy(() => import('screens/admin/CreateInvoice'))
const ProjectInvoices = React.lazy(() => import('screens/admin/ProjectInvoices'))
const ProjectInvoicesTable = React.lazy(() =>
  import('screens/client/Subcontract/components/ProjectInvoicesTable/ProjectInvoicesTable')
)
const CreateInvoices = React.lazy(() => import('screens/admin/Entrepreneurs/CreteInvoices'))
const CreateProject = React.lazy(() => import('screens/admin/CreateProject'))
const Claims = React.lazy(() =>
  import('screens/client/Subcontract/components/Verification/components/Claims')
)
const SubcontractHistoryTransaction = React.lazy(() =>
  import('screens/client/Profile/components/SubcontractHistoryTransaction')
)
const SettingsTaskCancellationInfo = React.lazy(() =>
  import('screens/admin/Settings/components/SettingsTaskCancellationInfo')
)
const ChannelsSettings = React.lazy(() =>
  import('screens/client/CryptoBot/components/AdvancedBotSettings')
)
const Notifications = React.lazy(() => import('screens/client/Notifications'))
const VotingList = React.lazy(() => import('screens/client/VotingList'))
const VotingDetails = React.lazy(() => import('screens/client/VotingList/components/VotingDetails'))
const Performers = React.lazy(() => import('screens/client/Subcontract/components/Performers'))
const CalendarPage = React.lazy(() => import('screens/client/Subcontract - Calendar'))

const SRCNManagement = React.lazy(() => import('screens/client/SRCNManagement/SRCNManagement'))
const FaucetManagement = React.lazy(() =>
  import('screens/client/FaucetManagement/FaucetManagement')
)

export type TRoutes = typeof adminRoutes | typeof clientRoutes
export const adminRoutes = [
  {
    path: ['/dashboard/requests'],
    exact: true,
    component: TimeOffRequests
  },
  {
    path: ['/dashboard/requests/create', '/dashboard/requests/:id/edit'],
    exact: true,
    component: NewRequestTimeOff
  },
  {
    path: '/dashboard/point-system/store',
    exact: true,
    component: Shop
  },
  {
    path: '/dashboard/point-system/store/create',
    exact: true,
    component: GoodsItem
  },
  {
    path: `/dashboard/point-system/store/:id/edit`,
    exact: true,
    component: GoodsItem
  },
  {
    path: '/dashboard/point-system/achievements',
    exact: true,
    component: AchievementsAdm
  },
  {
    path: [
      '/dashboard/point-system/achievements/create',
      `/dashboard/point-system/achievements/:id/edit`
    ],
    exact: true,
    component: AchievementItem
  },
  {
    path: '/dashboard/point-system/requests',
    exact: true,
    component: Requests
  },
  {
    path: '/dashboard/settings',
    exact: true,
    component: Settings
  },
  {
    path: '/dashboard/users',
    exact: true,
    component: Users
  },
  {
    path: '/dashboard/faq',
    exact: true,
    component: Information
  },
  {
    path: '/dashboard/policy',
    exact: true,
    component: PolicyAdmin
  },
  {
    path: '/dashboard/teamAdmin',
    exact: true,
    component: TeamAdmin
  },
  {
    path: '/dashboard/vacancies',
    exact: true,
    component: Vacancies
  },
  {
    path: ['/dashboard/vacancies/create', `/dashboard/vacancies/:id/edit`],
    exact: true,
    component: CreateVacancy
  },
  {
    path: '/dashboard/vacancies/:id',
    exact: true,
    component: Vacancy
  },
  {
    path: '/dashboard/foreign-profile',
    exact: true,
    component: ForeignProfile
  },
  {
    path: '/dashboard/profile/editprofile',
    exact: true,
    component: EditProfile
  },
  {
    path: '/dashboard/subcontract/verification',
    exact: true,
    component: VerificationAdmin
  },
  {
    path: '/dashboard/create-invoice',
    exact: true,
    component: CreateInvoice
  },
  {
    path: '/dashboard/projects/invoice/:id',
    exact: true,
    component: InvoiceModal
  },
  {
    path: '/dashboard/projects/invoices',
    exact: true,
    component: ProjectInvoices
  },
  {
    path: ENTERPRENEURS,
    exact: true,
    component: Entrepreneurs
  },
  {
    path: `${ENTERPRENEURS}/create-invoices`,
    exact: true,
    component: CreateInvoices
  },
  {
    path: '/dashboard/projects',
    exact: true,
    component: Projects
  },
  {
    path: '/dashboard/create-project',
    exact: true,
    component: CreateProject
  },
  {
    path: '/dashboard/projects/:id/edit',
    exact: true,
    component: CreateProject
  },
  {
    path: '/dashboard/subcontract',
    exact: true,
    component: SubcontractTasks
  },
  {
    path: '/dashboard/voting',
    exact: true,
    component: VotingList
  },
  {
    path: '/dashboard/projects/:id/history',
    exact: true,
    component: ProjectHistory
  },
  {
    path: '/dashboard/projects/:id',
    exact: true,
    component: Project
  },
  {
    path: '/dashboard/subcontract/verification/:id',
    exact: true,
    component: UserVerifiInfo
  },
  {
    path: '/dashboard/subcontract/:id',
    exact: true,
    component: Task
  },
  {
    path: '/dashboard/settings/task-cancellation/:id',
    exact: true,
    component: SettingsTaskCancellationInfo
  },
  {
    path: '/dashboard/tokens/srcn',
    component: SRCNManagement,
    exact: true
  },
  {
    path: '/dashboard/tokens/faucet',
    component: FaucetManagement,
    exact: true
  }
]

export const clientRoutes = [
  {
    path: '/dashboard/requests',
    exact: true,
    component: TimeOffRequests
  },
  {
    path: ['/dashboard/requests/create', '/dashboard/requests/:id/edit'],
    exact: true,
    component: NewRequestTimeOff
  },
  {
    path: '/dashboard/profile',
    exact: true,
    component: Profile
  },
  {
    path: '/dashboard/profile/editprofile',
    exact: true,
    component: EditProfile
  },
  {
    path: '/dashboard/point-system/profile/rating',
    exact: true,
    component: UserRating
  },
  {
    path: '/dashboard/point-system/store',
    exact: true,
    component: Goods
  },
  {
    path: '/dashboard/point-system/store/:id',
    exact: true,
    component: GoodsItemDetails
  },
  {
    path: '/dashboard/point-system/achievements',
    exact: true,
    component: Achievements
  },
  {
    path: '/dashboard/point-system/achievements/:id',
    exact: true,
    component: AchievementsItemDetails
  },
  {
    path: '/dashboard/point-system/basket',
    exact: true,
    component: Basket
  },
  {
    path: '/dashboard/point-system/notifications',
    exact: true,
    component: Notifications
  },
  {
    path: '/dashboard/faq',
    exact: true,
    component: Information
  },
  {
    path: '/dashboard/team',
    exact: true,
    component: Team
  },
  {
    path: '/dashboard/foreign-profile',
    exact: true,
    component: ForeignProfile
  },
  {
    path: '/dashboard/subcontract/profile/history-transaction',
    exact: true,
    component: SubcontractHistoryTransaction
  },
  {
    path: '/dashboard/Subcontract',
    exact: true,
    component: Subcontract
  },
  {
    path: '/dashboard/subcontract/planing-calendar',
    exact: true,
    component: PlaningCalendar
  },
  {
    path: '/dashboard/subcontract/verification',
    exact: true,
    component: Verification
  },
  {
    path: '/dashboard/subcontract/create-task',
    exact: true,
    component: CreateTask
  },
  {
    path: '/dashboard/subcontract/offer-task/:candidatId',
    exact: true,
    component: OfferTasks
  },
  {
    path: '/dashboard/subcontract/edittask/:taskId',
    exact: true,
    component: CreateTask
  },
  {
    path: '/dashboard/subcontract/interview-invitation',
    exact: true,
    component: InterviewInvitation
  },
  {
    path: '/dashboard/subcontract/verification/claims',
    exact: true,
    component: Claims
  },
  {
    path: '/dashboard/subcontract/voting',
    exact: true,
    component: VotingList
  },
  {
    path: '/dashboard/subcontract/voting/:id',
    exact: true,
    component: VotingDetails
  },
  {
    path: '/dashboard/cryptobot',
    exact: true,
    component: CryptoBot
  },
  {
    path: '/dashboard/privacy-terms',
    exact: true,
    component: Policy
  },
  {
    path: '/dashboard/subcontract/calendar',
    exact: true,
    component: CalendarPage
  },
  {
    path: '/dashboard/subcontract/performers',
    exact: true,
    component: Performers
  },
  {
    path: '/dashboard/subcontract/invoices',
    exact: true,
    component: ProjectInvoicesTable
  },
  {
    path: '/dashboard/subcontract/task-request',
    exact: true,
    component: TaskRequest
  },
  {
    path: '/dashboard/subcontract/offer',
    exact: true,
    component: TaskRequest
  },
  {
    path: '/dashboard/subcontract/task-payment',
    exact: true,
    component: TaskPayment
  },
  {
    path: '/dashboard/subcontract/:id',
    exact: true,
    component: Task
  },
  {
    path: '/dashboard/subcontract/invoices/:id',
    exact: true,
    component: ProjectInvoicesTable
  },
  {
    path: '/dashboard/channels-settings',
    exact: true,
    component: ChannelsSettings
  },
  {
    path: '/dashboard/tokens/srcn',
    component: SRCNManagement,
    exact: true
  },
  {
    path: '/dashboard/tokens/faucet',
    component: FaucetManagement,
    exact: true
  }
]
