import React, { FC } from 'react'
import { useDisconnect } from 'wagmi'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import Modal from 'components/Modal'
import { BaseButton } from 'components/UiKit/Button/BaseButton'

import styles from './WrongWalletModal.module.scss'

export const WrongWalletModal: FC = () => {
  const words = useSelector((state: TState) => state.global.language.words)
  const { disconnect } = useDisconnect()

  const title = words['user.main.modal.wrongWallet.title'] || 'Wallet Mismatch Detected'
  const text1 =
    words['user.main.modal.wrongWallet.text1'] ||
    'The connected wallet doesn`t match the one linked to your account.'
  const text2 =
    words['user.main.modal.wrongWallet.text2'] ||
    'Please switch to the correct wallet or disconnect and reconnect the right one.'
  const btnText = words['user.main.modal.wrongWallet.btnText'] || 'Disconnect Wallet'

  return (
    <Modal title={title} isShow={true} className={styles['modal']}>
      <div className={styles['container']}>
        <p className={styles['text']}>{text1}</p>
        <p className={styles['text']}>{text2}</p>
        <div className={styles['btn-block']}>
          <BaseButton
            size="lg"
            children={btnText}
            className={styles['btn']}
            onClick={() => disconnect()}
          />
        </div>
      </div>
    </Modal>
  )
}
