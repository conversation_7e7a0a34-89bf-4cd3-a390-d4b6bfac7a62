import React, { FC, Suspense, useEffect, useRef, useState } from 'react'
import { jwtDecode } from 'utils/jwt'
import { Switch, RouteComponentProps } from 'react-router-dom'
import { Redirect, Route, useLocation } from 'react-router'
import PrivateRoute from '../PrivatRoute'
import { mapStateToProps, mapDispatchToProps } from './App.container'
import {
  defaultContextValue,
  isManager,
  isUser,
  isAdmin,
  isSuperAdmin,
  TContextValue,
  UserContext,
  technorelyLanguage,
  isTeamMember
} from 'utils/user'
import { Elanguages } from 'store/global'
import { useDispatch, useSelector } from 'react-redux'
import { NotificationService } from 'socket/libs/services/notifications.socket.service'
import { socketService } from 'socket/socket'
import AuthRoute from 'elements/AuthRoute/AuthRoute.container'
import { TState } from 'store'
import { addAccessTokenInterceptor } from 'services/api'
import { useAuth } from 'hooks/auth.hook'
import isExpired from 'utils/checkExpAccessToken'
import CallbackRoute from 'elements/AuthRoute/CallbackRoute'
import { LoginLottie } from './components/LoginLottie'
import { setDayjsLocale } from 'utils/dayjs'
import { setMomentLocale } from 'utils/moment'
import { SkeletonMui } from 'components/Skeleton'

const LazyLogin = React.lazy(() => import('screens/auth/Login'))
const LazyDashboard = React.lazy(() => import('./components/Dashboard'))

type Props = RouteComponentProps &
  ReturnType<typeof mapDispatchToProps> &
  ReturnType<typeof mapStateToProps>

export type TLanguageObject = {
  fetchInfo: (language: string) => void
  languages: string[]
  currentLanguage: string
}

export const App: FC<Props> = ({
  currentLanguage,
  error: getLangErr,
  userId,
  decodedToken,
  fetchInformation,
  setAccessToken
}) => {
  let role = ''
  let tokenExp = 0

  if (decodedToken) {
    role = decodedToken.role
    tokenExp = decodedToken.exp
  }

  const [contextValue, setContextValue] = useState<TContextValue>(defaultContextValue)
  const words = useSelector((state: TState) => state.global.language.words)
  const dispatch = useDispatch()
  const intervalRef: any = useRef(null)
  const {
    getAccessTokenSilently,
    logoutWithRedirect,
    isAuthenticated,
    error: errorAuth,
    isLoading: isAuthloading
  } = useAuth()

  const location = useLocation()
  const isSignin = location.pathname.includes('/signin') || location.pathname.includes('/callback')

  const handleAuthToken = async () => {
    try {
      const token = await getAccessTokenSilently()
      if (token) {
        addAccessTokenInterceptor(token)
        const decodedAccessToken = jwtDecode(token)

        if (!decodedAccessToken || isExpired(decodedAccessToken.exp)) {
          logoutWithRedirect()
          return
        }

        const pay = {
          token,
          decodedToken: decodedAccessToken
        }
        dispatch(setAccessToken(pay))
      }

      if (!token) {
        logoutWithRedirect()
        return
      }
    } catch (err) {
      logoutWithRedirect()
    }
  }

  useEffect(() => {
    if (isAuthenticated && !isSignin && !isAuthloading && tokenExp) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      intervalRef.current = setInterval(() => {
        const isTokenExpired = tokenExp ? isExpired(tokenExp) : true

        if (isTokenExpired) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
          logoutWithRedirect()
        }
      }, 10000)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [isAuthenticated, isAuthloading, tokenExp, isSignin])

  useEffect(() => {
    if (!isSignin && errorAuth) {
      logoutWithRedirect()
    }

    if (isAuthenticated && !isSignin && !isAuthloading) {
      handleAuthToken()
    }
  }, [isAuthenticated, isAuthloading])

  const fetchInfo = (language: string) => {
    localStorage.setItem(technorelyLanguage, language)
    setDayjsLocale(language)
    setMomentLocale(language)
    fetchInformation(language)
  }

  const fetchInformationWithSave = (language: string) => {
    const currentLang = localStorage.getItem(technorelyLanguage) || language
    localStorage.setItem(technorelyLanguage, currentLang)
    setDayjsLocale(currentLang)
    setMomentLocale(currentLang)
    fetchInformation(currentLang)
  }

  const languageObject = {
    fetchInfo,
    languages: Object.values(Elanguages),
    currentLanguage
  }

  useEffect(() => {
    fetchInformationWithSave(currentLanguage)
  }, [])

  useEffect(() => {
    let userData

    if (role && userId) {
      socketService.connect()

      const notificationService = new NotificationService(socketService.getSocket())
      notificationService.notificationConnect(userId, dispatch)

      setContextValue({
        data: userData,
        superAdmin: isSuperAdmin(role),
        admin: isAdmin(role),
        manager: isManager(role),
        user: isUser(role),
        teamMember: isTeamMember(role)
      } as TContextValue)
    }
  }, [role, userId])

  useEffect(() => {
    if (getLangErr) {
      setTimeout(() => {
        document.location.reload()
      }, 10000)
    }
  }, [getLangErr])

  const content = () => {
    if ((getLangErr || !Object.keys(words).length || isAuthloading) && !isSignin) {
      return <SkeletonMui variant="rectangular" height={400} /> // Use skeleton instead of spinner
      // return <LoginLottie />
    }

    if (!isSignin && !isAuthloading && !isAuthenticated) {
      logoutWithRedirect()
      return <LoginLottie />
    }

    return (
      <UserContext.Provider value={contextValue}>
        <Suspense fallback={<LoginLottie />}>
          <Switch>
            {isAuthenticated && role && (
              <PrivateRoute
                path="/dashboard"
                component={LazyDashboard}
                languageObject={languageObject}
              />
            )}
            <AuthRoute path="/signin" component={LazyLogin} />
            <CallbackRoute path="/callback" />

            <Route render={() => <Redirect to="/signin" />} />
          </Switch>
        </Suspense>
      </UserContext.Provider>
    )
  }

  return content()
}
