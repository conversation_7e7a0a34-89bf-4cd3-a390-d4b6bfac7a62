import React, { FC, useEffect } from 'react'
import { withRouter } from 'react-router-dom'
import { WagmiProvider } from 'wagmi'
import { QueryClientProvider } from '@tanstack/react-query'

import { TPrivateRouter } from './PrivatRoute.model'
import Dashboard from 'elements/App/components/Dashboard'
import { wagmiConfig } from './components/wagmiConfig'
import { queryClient } from './components/queryClient'

const PrivateRoute: FC<TPrivateRouter> = ({ languageObject, decodedToken, ...rest }) => {
  console.log('***_PRIVATE_RENDER')
  useEffect(() => {
    console.log('***_PRIVATE_MOUNTED')
  }, [])

  return (
    <WagmiProvider config={wagmiConfig}>
      <QueryClientProvider client={queryClient}>
        <Dashboard decodedToken={decodedToken} languageObject={languageObject} {...rest} />
      </QueryClientProvider>
    </WagmiProvider>
  )
}

export default withRouter(PrivateRoute)

// TODO: Consider deleting these dependincies
//  "ethers",
// "@ethersproject/*",
// "web3",
// "web3-utils",
// "@web3-react/core",
// "@web3-react/injected-connector",
// "@web3-react/types",
// "@web3-react/walletconnect-connector"
