import { RouteComponentProps } from 'react-router-dom'
import { mapDispatchToProps, mapStateToProps } from './PrivatRoute.container'
import { TLanguageObject } from 'elements/App/App'
import { IDecodedToken } from 'screens/auth/Login/Login.model'

export type TPrivateRouter = RouteComponentProps &
  ReturnType<typeof mapStateToProps> &
  ReturnType<typeof mapDispatchToProps> & {
    component: any
    exact?: boolean
    path: string
    languageObject: TLanguageObject
    decodedToken: IDecodedToken
  }
