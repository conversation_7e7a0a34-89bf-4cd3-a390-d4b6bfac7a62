import { defaultWagmiConfig } from '@web3modal/wagmi/react/config'
import { config } from 'globalConfigs'

// 1. Get projectId from https://cloud.walletconnect.com
const projectId = config.walletConnectProjectId

// 2. Create wagmiConfig
const metadata = {
  name: 'Platform',
  description: 'Platform',
  url: 'https://web3modal.com', // origin must match your domain & subdomain
  icons: ['https://avatars.githubusercontent.com/u/37784886']
}

const chains = [config.blockchain.chain] as const
export const wagmiConfig = defaultWagmiConfig({
  chains,
  projectId,
  metadata,
  enableWalletConnect: true,
  enableEIP6963: true
})
