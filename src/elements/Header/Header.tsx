import React, { FC, useEffect, useMemo, useState } from 'react'
import { useLocation } from 'react-router'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { useWeb3Modal } from '@web3modal/wagmi/react'
import cls from 'classnames'
import { useAccount, useDisconnect } from 'wagmi'

import styles from './Header.module.scss'
import { IHeader } from './Header.model'
import { OpenBasketPopup } from 'screens/client/Basket/components/OpenBasketPopup/OpenBasketPopup'
import { getTimer } from 'screens/auth/Login/Login.thunx'
import { useUser } from '../../hooks/useUser'
import { HeaderMenu } from './components/HeaderMenu'
import { BaseButton } from 'components/UiKit/Button/BaseButton'
import PolicyModal from 'components/PolicyModal/PolicyModal'
import { OpenNotificationsPopup } from 'screens/client/Notifications/components/OpenNotificationsPopup/OpenNotificationsPopup'
import { config, EAcceptPolicyType, EPolicy } from 'globalConfigs'
import { getAccessTokenFromStorage } from 'helpers/getAccessTokenFromStorage'
import { isEmpty } from 'utils/lodashReplacements'
import { computeIsUserVerified } from 'helpers/computeIsUserVerified'
import { useAvailableFunds } from 'hooks/useAvailableFunds'

export const Header: FC<IHeader> = ({
  user,
  trlBalance,
  userPhoto,
  totalQuantity,
  totalPrice,
  basket,
  addToCart,
  removeFromCart,
  pulling,
  totalNotifications,
  notifications,
  fetchNotifications,
  viewNotification,
  getPointsSettings,
  getUserPhoto,
  isSideBarCollapsed,
  updateUserWallet,
  currentLanguage,
  getCurrentUserData,
  getPolicyModal,
  policyContentModal,
  policyContentTypeModal,
  acceptPrivacyPolicy,
  acceptCookiePolicy,
  acceptTermsOfService,
  verification,
  verificationStatus,
  getTrlBalanceThunk,
  acceptPolicyByUser,
  clearPolicyModal
}) => {
  const { userData } = useUser()
  const { disconnect } = useDisconnect()
  const isTeamMember = userData.teamMember
  const isUserSide = userData.user || isTeamMember || userData.manager
  const words = useSelector((state: TState) => state.global.language.words)
  const { userBids } = useAvailableFunds()

  const locationPath = useLocation().pathname

  const notAcception = useMemo(() => {
    const acception: Partial<Record<EPolicy, boolean>> = {}

    const policies: [EPolicy, boolean | undefined][] = [
      [EPolicy.COOKIE_POLICY, acceptCookiePolicy],
      [EPolicy.PRIVACY_POLICY, acceptPrivacyPolicy],
      [EPolicy.TERMS_OF_SERVICE, acceptTermsOfService]
    ]

    // Fill object only valid value (not `undefined`)
    policies.forEach(([key, value]) => {
      if (value !== undefined) acception[key] = value
    })

    if (Object.keys(acception).length === 0) return []

    // Leave only those where `false`
    return Object.entries(acception)
      .filter(([_, value]) => !value)
      .map(([key]) => key as EPolicy)
  }, [acceptPrivacyPolicy, acceptCookiePolicy, acceptTermsOfService])

  const [isOpenModal, setOpenModal] = useState(false)

  const { address, isConnected } = useAccount()
  const { open } = useWeb3Modal()
  const token = getAccessTokenFromStorage()

  const isUserVerified = computeIsUserVerified({ verification, verificationStatus })

  const handleOpenWalletConnect = () => isUserVerified && open()

  useEffect(() => {
    if (isUserVerified && !isEmpty(user) && !user.walletAddress && address) {
      updateUserWallet(user.id, address, disconnect)
    }
  }, [user, address])

  useEffect(() => {
    if (isConnected && address && isUserSide) {
      getTrlBalanceThunk()
    }
  }, [isConnected, address, isUserSide])

  useEffect(() => {
    if (!isUserSide) return
    const exludePaths = ['/dashboard/privacy-terms'].includes(locationPath)

    if (exludePaths) return

    if (notAcception.length) {
      const req = {
        parsedType: notAcception[0],
        currentLanguage
      }
      getPolicyModal(req)
      setOpenModal(() => true)
    } else {
      setOpenModal(() => false)
      clearPolicyModal()
    }
  }, [notAcception, locationPath, isUserSide])

  useEffect(() => {
    pulling()
    if (token) {
      getCurrentUserData()
    }

    return () => {
      clearTimeout(getTimer())
    }
  }, [])

  useEffect(() => {
    if (isTeamMember) {
      getPointsSettings()
    }
  }, [isTeamMember])

  useEffect(() => {
    if (!userPhoto) {
      setTimeout(() => {
        getUserPhoto(user.id)
      }, 500)
    }
  }, [userPhoto])

  const acceptPolicy = () => {
    const req = {
      parsedType: policyContentTypeModal,
      solution: EAcceptPolicyType.ACCEPT
    }
    acceptPolicyByUser(req)
  }

  return (
    <>
      <section
        className={cls({
          [styles.container]: true,
          [styles.sidebar_collapsed]: isSideBarCollapsed
        })}
      >
        {isUserSide && (
          <div className={styles.points}>
            <div className={styles.subcontract_balance}>
              <p className={styles.subcontract_balance_item}>
                {words['user.subcontract.bids']}:<span>{userBids}</span>
              </p>
            </div>
            {(isTeamMember || userData.manager) && (
              <OpenBasketPopup
                totalQuantity={totalQuantity}
                totalPrice={totalPrice}
                basket={basket}
                addToCart={addToCart}
                removeFromCart={removeFromCart}
                balance={user.balance}
              />
            )}
            <OpenNotificationsPopup
              totalQuantity={totalNotifications}
              notifications={notifications}
              fetchNotifications={fetchNotifications}
              selectNotification={viewNotification}
            />
            <HeaderMenu
              className={styles.header_menu}
              photo={userPhoto || ''}
              userSide={isUserSide}
              currentLanguage={currentLanguage}
            />
            <div className={styles.points_trl}>
              {isUserVerified && isConnected ? (
                <a
                  className={cls([
                    styles['buy-token'],
                    { [styles['disabled']]: !config.buyTokenUrl }
                  ])}
                  href={config.buyTokenUrl}
                  target="_blank"
                  rel="noreferrer"
                >
                  {words['user.subcontract.buyTRL']}
                </a>
              ) : (
                <BaseButton
                  children={'Connect'}
                  size="smb"
                  type="button"
                  onClick={handleOpenWalletConnect}
                  disabled={!isUserVerified}
                  title={isUserVerified ? 'Connect wallet' : 'User is not verified'}
                />
              )}

              {isConnected && trlBalance && (
                <span className={styles.points_balance}>
                  {trlBalance.value}

                  <span>{trlBalance.symbol}</span>
                </span>
              )}
            </div>
          </div>
        )}
        {!isUserSide && (
          <div className={styles.points}>
            <OpenNotificationsPopup
              totalQuantity={totalNotifications}
              notifications={notifications}
              popupClasses={styles['admin-notification']}
              fetchNotifications={fetchNotifications}
              selectNotification={viewNotification}
            />
            <HeaderMenu
              photo={userPhoto || ''}
              userSide={isUserSide}
              currentLanguage={currentLanguage}
            />
          </div>
        )}
        {isOpenModal && policyContentModal && (
          <PolicyModal
            content={policyContentModal}
            acceptPolicy={acceptPolicy}
            isPreviewMode={false}
          />
        )}

        {/* <Modal isShow={openModal} onClose={closeRedirectModal} className={styles['redirect-modal']}>
          <p>{words['user.popup.infoIsRequired']}</p>
          <div className={styles['required-info-image']} />
          <div className={styles['btn-container']}>
            <Button type="button" className={styles['send-friend']} onClick={closeRedirectModal}>
              {words['user.popup.fillIn']}
            </Button>
          </div>
        </Modal> */}
      </section>
    </>
  )
}
