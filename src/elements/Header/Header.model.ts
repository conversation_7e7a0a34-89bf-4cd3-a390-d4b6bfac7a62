import { ICartItem } from '../../screens/client/Basket/Basket.model'
import { mapDispatchToProps, mapStateToProps } from './Header.container'

export type IHeader = ReturnType<typeof mapDispatchToProps> &
  ReturnType<typeof mapStateToProps> & {
    user: any
    totalQuantity: number
    totalPrice: number
    pulling: () => void
    basket: ICartItem[]
    addToCart: (goods: ICartItem) => void
    removeFromCart: (goods: ICartItem) => void
    getPointsSettings: () => void
    pointsFundPerMonth: number
    pointsFund: number
    dataUsers: any
    getUserPhoto: any
    userPhoto: string
    trlTokenPrice: number
    isSideBarCollapsed?: boolean
  }
