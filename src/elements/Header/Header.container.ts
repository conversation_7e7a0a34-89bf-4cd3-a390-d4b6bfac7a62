import { connect } from 'react-redux'
import { bindActionCreators, Dispatch } from 'redux'
import { Header as HeaderComponent } from './Header'
import { getCurrentUserData, getUserPhoto, pulling } from '../../screens/auth/Login/Login.thunx'
import { addToCart, removeFromCart } from '../../screens/client/Basket/Basket.actions'
import {
  getTotalPriseSelector,
  getTotalQuantitySelector,
  getDataSelector
} from '../../screens/client/Basket/Basket.reducer'
import { getPointsSettings } from 'screens/admin/Settings/components/SettingsPointsSystem/SettingsPointsSystem.thunx'
import { acceptPolicyByUser, getPolicyModal } from 'screens/client/Policy/Policy.thunx'
import {
  fetchNotifications,
  viewNotification
} from 'screens/client/Notifications/Notifications.thunx'
import { getTrlBalanceThunk } from 'store/modules/balance/balance.thunk'
import { updateUserWallet } from 'screens/client/EditProfile/EditProfile.thunx'
import { clearPolicyModal } from 'screens/client/Policy/Policy.actions'

export const mapStateToProps = (state: any) => ({
  user: state.auth.data,
  trlBalance: state.client.balance.trlBalance,
  totalQuantity: getTotalQuantitySelector(state),
  totalPrice: getTotalPriseSelector(state),
  basket: getDataSelector(state),
  notifications: state.client.notifications.data,
  totalNotifications: state.client.notifications.totalQuantity,
  pointsFundPerMonth: state.admin.settings.pointsSystem.result.pointsFundPerMonth,
  pointsFund: state.admin.settings.pointsSystem.result.pointsFund,
  dataUsers: state.client.newRequestTimeOff.users.results,
  userPhoto: state.auth.avatar,
  trlTokenPrice: state.client.web3.trlTokenPrice,
  policyContentModal: state.client.policy.policyContentModal,
  policyContentTypeModal: state.client.policy.policyContentTypeModal,
  acceptPrivacyPolicy: state.auth.data.acceptPrivacyPolicy,
  acceptCookiePolicy: state.auth.data.acceptCookiePolicy,
  acceptTermsOfService: state.auth.data.acceptTermsOfService,
  verification: state.auth.data.verification,
  verificationStatus: state.auth.data.verificationStatus,
  currentLanguage: state.global.language.currentLanguage
})

export const mapDispatchToProps = (dispatch: Dispatch) => ({
  ...bindActionCreators(
    {
      pulling,
      getPointsSettings,
      addToCart,
      removeFromCart,
      getUserPhoto,
      fetchNotifications,
      viewNotification,
      updateUserWallet,
      getCurrentUserData,
      getPolicyModal,
      acceptPolicyByUser,
      clearPolicyModal,
      getTrlBalanceThunk
    },
    dispatch
  )
})

export const Header = connect(
  mapStateToProps,
  mapDispatchToProps
)(HeaderComponent)

// export default connect(
//   mapStateToProps,
//   mapDispatchToProps
// )(withRouter(Header))
