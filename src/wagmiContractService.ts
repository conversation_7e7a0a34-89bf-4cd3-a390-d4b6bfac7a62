/* eslint-disable prettier/prettier */
import { readContract, writeContract, waitForTransactionReceipt, simulateContract } from '@wagmi/core'
import { getContractAbiByApi } from 'utils/getContractAbi'
import { EMessageCodes } from 'types/EMessageCodes'
import type { Abi } from 'viem'
import { wagmiConfig } from 'elements/PrivatRoute/components/wagmiConfig'

const abiCache = new Map<string, Abi>()

export interface ContractService {
  address: string
  read: (functionName: string, args?: unknown[]) => Promise<any>
  write: (functionName: string, args?: unknown[], account?: `0x${string}`, value?: bigint) => Promise<`0x${string}`>
  writeAndWait: (functionName: string, args?: unknown[], account?: `0x${string}`, value?: bigint) => Promise<`0x${string}`>
  simulate: (functionName: string, args?: unknown[], account?: `0x${string}`, value?: bigint) => Promise<void>
}

export const getContractService = async (
  address: string,
  initAbiAddress?: string,
  initAbi?: Abi
): Promise<ContractService> => {
  try {
    const cacheKey = (initAbiAddress || address).toLowerCase()
    const abi: Abi =
      initAbi ||
      abiCache.get(cacheKey) ||
      (await getContractAbiByApi(initAbiAddress || address))

   
    if (!abiCache.has(cacheKey)) abiCache.set(cacheKey, abi)

    return {
      address,

      read: async (functionName, args = []) => {
        const params: any = {
          abi,
          address: address as `0x${string}`,
          functionName,
        }
        if (args.length) params.args = args
        // @ts-ignore
        return await readContract(wagmiConfig as any, params)
      },

      write: async (functionName, args = [], account, value) => {
        const params: any = {
          abi,
          address: address as `0x${string}`,
          functionName,
        }
        if (args.length) params.args = args
        if (account) params.account = account
        if (value !== undefined) params.value = value
        // @ts-ignore
        const hash = await writeContract(wagmiConfig as any, params)
        return hash
      },

      writeAndWait: async (functionName, args = [], account, value) => {
        const params: any = {
          abi,
          address: address as `0x${string}`,
          functionName,
        }
        if (args.length) params.args = args
        if (account) params.account = account
        if (value !== undefined) params.value = value
        // @ts-ignore
        const hash = await writeContract(wagmiConfig as any, params)
        const receipt = await waitForTransactionReceipt(wagmiConfig as any, { hash })

        if (receipt.status !== 'success') {
          throw new Error(EMessageCodes.ERR_TRANSACTION_FAILED)
        }
        return hash
      },

      simulate: async (functionName, args = [], account, value) => {
        const params: any = {
          abi,
          address: address as `0x${string}`,
          functionName,
        }
        if (args.length) params.args = args
        if (account) params.account = account
        if (value !== undefined) params.value = value
        // @ts-ignore
        await simulateContract(wagmiConfig as any, params)
      },
    }
  } catch {
    throw new Error(EMessageCodes.ERR_CANT_CREATE_CONTRACT_INSTANCE)
  }
}
