<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12647_28745)">
<circle cx="31" cy="26" r="16" fill="url(#paint0_linear_12647_28745)"/>
</g>
<rect x="30.332" y="22" width="1.33333" height="8" rx="0.666667" fill="white"/>
<rect x="35" y="25.3335" width="1.33334" height="8" rx="0.666668" transform="rotate(90 35 25.3335)" fill="white"/>
<defs>
<filter id="filter0_d_12647_28745" x="0" y="0" width="62" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="7.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.121569 0 0 0 0 0.121569 0 0 0 0 0.921569 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12647_28745"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12647_28745" result="shape"/>
</filter>
<linearGradient id="paint0_linear_12647_28745" x1="31" y1="10" x2="31" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="#1F1FEB"/>
<stop offset="1" stop-color="#3737ED"/>
</linearGradient>
</defs>
</svg>
