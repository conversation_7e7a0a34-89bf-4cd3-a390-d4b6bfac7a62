<svg width="126" height="81" viewBox="0 0 126 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_11816_26271)">
<rect x="4" width="112" height="70" rx="10" fill="#EFEFFF"/>
</g>
<g clip-path="url(#clip0_11816_26271)">
<path d="M28.5 32.4816C32.1691 32.4816 35.1435 29.4636 35.1435 25.7408C35.1435 22.0179 32.1691 19 28.5 19C24.8308 19 21.8564 22.0179 21.8564 25.7408C21.8564 29.4636 24.8308 32.4816 28.5 32.4816Z" fill="#110F30"/>
<path d="M28.5 34.8516C21.0428 34.8516 15 40.9828 15 48.5492C15 50.1478 16.2779 51.4417 17.8507 51.4417H39.1465C40.7221 51.4417 41.9973 50.1451 41.9973 48.5492C42 40.9828 35.9545 34.8516 28.5 34.8516Z" fill="#110F30"/>
</g>
<rect x="54" y="23" width="48" height="8" rx="2" fill="white"/>
<rect x="54" y="39" width="32" height="8" rx="2" fill="white"/>
<circle cx="108" cy="63" r="18" fill="#FF5F1B"/>
<g clip-path="url(#clip1_11816_26271)">
<path d="M111.688 69.658H104.318C104.257 69.658 104.207 69.6146 104.201 69.555C104.184 69.4032 104.173 69.2461 104.173 69.0889C104.173 68.6011 104.373 68.032 104.773 67.387C105.083 66.8829 105.516 66.3301 106.066 65.7501C106.987 64.7637 107.925 64.0157 107.936 64.0049C107.98 63.9723 108.041 63.9723 108.086 64.0049C108.097 64.0103 109.029 64.7582 109.956 65.7501C110.5 66.3301 110.933 66.8829 111.249 67.387C111.643 68.0265 111.849 68.6011 111.849 69.0889C111.849 69.2461 111.838 69.3978 111.821 69.555C111.799 69.6146 111.749 69.658 111.688 69.658Z" fill="white"/>
<path d="M101.343 70.3782H101.87C101.809 70.0313 101.776 69.679 101.776 69.3213C101.776 68.3511 102.253 67.1424 103.197 65.7386C103.768 64.8877 104.501 63.9663 105.383 62.9961C104.501 62.0259 103.768 61.1045 103.197 60.2535C102.253 58.8498 101.776 57.6465 101.776 56.6763C101.776 56.3186 101.809 55.9663 101.87 55.6194H101.343C100.982 55.6194 100.649 55.4839 100.394 55.2346C100.139 54.9853 100 54.6601 100 54.3077C100 53.5869 100.599 52.9961 101.343 52.9961H114.657C115.018 53.0015 115.356 53.137 115.606 53.3863C115.861 53.6302 116 53.9609 116 54.3132C116 55.034 115.401 55.6248 114.657 55.6248H114.13C114.191 55.9663 114.224 56.324 114.224 56.6817C114.224 57.6519 113.747 58.8606 112.803 60.2644C112.232 61.1153 111.499 62.0367 110.617 63.0069C111.499 63.9771 112.232 64.8985 112.803 65.7495C113.747 67.1533 114.224 68.3565 114.224 69.3267C114.224 69.6844 114.191 70.0367 114.13 70.3836H114.657C115.018 70.3836 115.351 70.5191 115.606 70.7685C115.861 71.0124 116 71.343 116 71.6899C116 72.4107 115.395 73.0015 114.657 73.0015H101.343C100.982 73.0015 100.649 72.866 100.394 72.6167C100.139 72.3674 100 72.0422 100 71.6899C100 70.969 100.605 70.3782 101.343 70.3782ZM109.213 63.446C108.974 63.1912 108.974 62.8118 109.213 62.5571C110.234 61.4731 111.061 60.4595 111.677 59.5381C112.459 58.3674 112.875 57.3809 112.875 56.6817C112.875 56.324 112.837 55.9663 112.753 55.6248H103.247C103.163 55.9717 103.125 56.324 103.125 56.6817C103.125 57.3809 103.541 58.3728 104.323 59.5381C104.939 60.4595 105.772 61.4731 106.787 62.5571C107.026 62.8118 107.026 63.1912 106.787 63.446C105.766 64.53 104.939 65.5435 104.323 66.4649C103.541 67.6357 103.125 68.6221 103.125 69.3213C103.125 69.679 103.163 70.0367 103.247 70.3782H112.753C112.837 70.0313 112.875 69.679 112.875 69.3213C112.875 68.6221 112.459 67.6302 111.677 66.4649C111.055 65.5435 110.228 64.53 109.213 63.446Z" fill="white"/>
<path d="M108.003 62.0158C107.98 62.0158 107.958 62.0104 107.942 61.9996C107.886 61.967 106.521 61.1757 105.749 59.9562C105.727 59.9183 105.727 59.8749 105.744 59.837C105.766 59.799 105.805 59.7773 105.849 59.7773H110.156C110.2 59.7773 110.239 59.799 110.261 59.837C110.284 59.8749 110.284 59.9183 110.256 59.9562C109.479 61.1757 108.119 61.967 108.064 61.9996C108.041 62.0158 108.019 62.0158 108.003 62.0158Z" fill="white"/>
</g>
<path d="M114.642 45.0111C115.145 45.0115 115.627 45.2118 115.983 45.5679L125.387 54.9883C125.743 55.3444 125.942 55.8271 125.942 56.3302L125.931 69.6416C125.93 70.1447 125.73 70.6271 125.374 70.9826L115.954 80.3873C115.598 80.7429 115.115 80.9423 114.612 80.9419L101.3 80.9308C100.797 80.9304 100.315 80.7301 99.9593 80.374L90.5545 70.9536C90.1991 70.5976 89.9996 70.1149 90 69.6116L90.0111 56.3002C90.0115 55.7971 90.2118 55.3147 90.5679 54.9593L99.9884 45.5545C100.344 45.1991 100.827 44.9996 101.33 45L114.642 45.0111Z" fill="#E12F75"/>
<path d="M106.52 72.4211C106.147 72.0613 105.961 71.6294 105.961 71.1256C105.961 70.6218 106.147 70.1899 106.52 69.83C106.912 69.4702 107.392 69.2902 107.961 69.2902C108.51 69.2902 108.981 69.4702 109.373 69.83C109.765 70.1899 109.961 70.6218 109.961 71.1256C109.961 71.6114 109.765 72.0433 109.373 72.4211C108.981 72.781 108.51 72.9609 107.961 72.9609C107.392 72.9609 106.912 72.781 106.52 72.4211ZM106.108 54.4454C106.108 53.9776 106.275 53.6177 106.608 53.3658C106.961 53.0959 107.402 52.9609 107.932 52.9609C108.461 52.9609 108.902 53.0959 109.255 53.3658C109.608 53.6357 109.784 53.9956 109.784 54.4454C109.784 55.633 109.716 57.6123 109.579 60.3833C109.441 63.1544 109.373 65.1337 109.373 66.3213C109.373 66.6451 109.216 66.8971 108.902 67.077C108.608 67.2569 108.284 67.3469 107.932 67.3469C106.971 67.3469 106.49 67.005 106.49 66.3213C106.49 65.1337 106.422 63.1544 106.284 60.3833C106.167 57.6123 106.108 55.633 106.108 54.4454Z" fill="white"/>
<defs>
<filter id="filter0_d_11816_26271" x="0" y="0" width="120" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11816_26271"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11816_26271" result="shape"/>
</filter>
<clipPath id="clip0_11816_26271">
<rect width="27" height="32" fill="white" transform="translate(15 19)"/>
</clipPath>
<clipPath id="clip1_11816_26271">
<rect width="16" height="20" fill="white" transform="matrix(-1 0 0 1 116 53)"/>
</clipPath>
</defs>
</svg>
