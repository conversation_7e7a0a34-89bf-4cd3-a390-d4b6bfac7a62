<svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<circle cx="80" cy="80" r="65" fill="#3737ED"/>
</g>
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="15" y="15" width="130" height="130">
<circle cx="80" cy="80" r="65" fill="#C41BFF"/>
</mask>
<g mask="url(#mask0)">
<circle cx="80" cy="158" r="57" fill="white"/>
<circle cx="80" cy="68" r="25" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="160" height="160" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.215686 0 0 0 0 0.215686 0 0 0 0 0.929412 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
