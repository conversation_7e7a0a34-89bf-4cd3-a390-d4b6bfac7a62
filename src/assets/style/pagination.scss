@import 'colors';
@import 'variables';
@import 'mixins';

.rc-pagination {
  display: flex;
  justify-content: center;

  &-prev {
    cursor: pointer;

    &[aria-disabled='false'] {
      svg path {
        stroke: $blue;
      }
    }
  }

  &-next {
    cursor: pointer;

    &[aria-disabled='false'] {
      svg path {
        stroke: $blue;
      }

    }
  }

  &-item {
    cursor: pointer;

    @include pagination-item;

    &-active {
      @include pagination-active;
    }
  }
}