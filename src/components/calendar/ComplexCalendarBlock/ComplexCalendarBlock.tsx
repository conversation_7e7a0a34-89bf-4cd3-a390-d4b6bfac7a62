import React from 'react'
import { config } from 'globalConfigs'
import MiniCalendar from '../MiniCalendar/MiniCalendar'
import {
  CalendarEventsFilter,
  IEventFilterCheckbox
} from '../CalendarEventsFilter/CalendarEventsFilter'
import CalendarPickTimeSlotBlock, {
  IPickTimeSlotBlockProps
} from '../CalendarPickTimeSlotBlock/CalendarPickTimeSlotBlock'
import BigCalendar from '../BigCalendar/BigCalendar'
import { InvitationsDataBlock } from 'components/InvitationsDataBlock/InvitationsDataBlock'

import { IBigCalendarsProps } from '../BigCalendar/BigCalender.model'
import { ISelectedSlot } from 'models/calendar-models'

import styles from './ComplexCalendarBlock.module.scss'

type TDate = Date | null | undefined | [Date | null, Date | null]

interface IComplexCalendarBlockProps {
  currentDate: TDate
  setCurrentDate: (value: TDate) => void
  filterCheckboxes: IEventFilterCheckbox[][]
  selectedSlots?: ISelectedSlot[]
  setSelectedSlots?: (array: ISelectedSlot[]) => void
  rightSidePreChildren?: React.ReactNode
  propsPickTimeBlock?: Pick<IPickTimeSlotBlockProps, 'buttonText' | 'onSubmit'>
  propsBigCalendar: Pick<
    IBigCalendarsProps,
    | 'isEventModal'
    | 'events'
    | 'setEventModal'
    | 'loadEvents'
    | 'onDeleteEventGC'
    | 'onDeleteInviteReqSlot'
  >
}

const ComplexCalendarBlock = ({
  currentDate,
  setCurrentDate,
  filterCheckboxes,
  selectedSlots = [],
  setSelectedSlots = () => undefined,
  rightSidePreChildren,
  propsPickTimeBlock,
  propsBigCalendar
}: IComplexCalendarBlockProps) => {
  const deleteSelectedSlot = (slotId: ISelectedSlot['slotId']) => {
    setSelectedSlots(selectedSlots.filter(slot => slot.slotId !== slotId))
  }
  const setNewSelectedSlots = (array: ISelectedSlot[]) => {
    if (array.length > config.timeSlotsLimit) return
    setSelectedSlots(array)
  }

  return (
    <div className={styles['complex-block']}>
      <div className={styles['left-side']}>
        <MiniCalendar value={currentDate} onChange={setCurrentDate} />
        <CalendarEventsFilter checkboxes={filterCheckboxes} />
        {!!propsPickTimeBlock && (
          <CalendarPickTimeSlotBlock
            slots={selectedSlots}
            deleteSlot={deleteSelectedSlot}
            {...propsPickTimeBlock}
          />
        )}
      </div>

      <div className={styles['right-side']}>
        {rightSidePreChildren}
        <BigCalendar
          currentDate={currentDate}
          selectedSlots={selectedSlots}
          setSelectedSlots={setNewSelectedSlots}
          {...propsBigCalendar}
        />
        <InvitationsDataBlock className={styles['invitations-block']} />
      </div>
    </div>
  )
}

export default ComplexCalendarBlock
