@import 'assets/style/colors';

.slot {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
}

.slot-datetime {
  display: flex;
  gap: 6px;
  font-size: 14px;
  color: $dark-grey;
}

.slot-divider {
  color: $light-grey;
}

.checkbox-block {
  & > div {
    margin: 0;
    display: flex;
  }

  ::before {
    margin: 0;
  }
}

.delete-btn {
  cursor: pointer;
  display: flex;

  svg {
    transition: all 0.3s;
  }

  svg:hover {
    scale: 1.3;
  }
}
