import React, { <PERSON> } from 'react'
import cls from 'classnames'
import dayjs from 'utils/dayjs'
import { Icons } from 'components/Icons'
import Checkbox from 'components/UiKit/Checkbox/Checkbox'

import styles from './CalendarTimeSlot.module.scss'

interface ICalendarTimeSlotProps {
  slot: {
    slotId: string
    start: Date
    end: Date
  }
  onDelete?: () => void
  checked?: boolean
  onCheck?: (value: boolean) => void
  className?: string
}

const CalendarTimeSlot: FC<ICalendarTimeSlotProps> = ({
  slot,
  onDelete,
  checked,
  onCheck,
  className
}) => (
  <div className={cls([styles['slot'], { [className || '']: className }])}>
    <div className={styles['slot-datetime']}>
      <div className={styles['slot-date']}>{dayjs(slot.start).format('DD.MM.YYYY')}</div>
      <span className={styles['slot-divider']}>|</span>
      <div className={styles['slot-time']}>
        {dayjs(slot.start).format('LT')} - {dayjs(slot.end).format('LT')}
      </div>
    </div>

    {onCheck && (
      <div className={styles['checkbox-block']}>
        <Checkbox
          name={slot.slotId}
          checked={checked}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onCheck(e.target.checked)}
        />
      </div>
    )}
    {onDelete && (
      <Icons className={styles['delete-btn']} icon={'close'} onClick={() => onDelete()} />
    )}
  </div>
)

export default CalendarTimeSlot
