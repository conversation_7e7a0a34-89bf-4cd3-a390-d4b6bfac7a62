import React, { FC } from 'react'
import dayjs from 'utils/dayjs'
import cls from 'classnames'
import { ISuggestedDates } from 'models/calendar-models'
import { Icons } from 'components/Icons'

import styles from './CalendarSelectedTimeSlot.module.scss'

interface IProps {
  slot: ISuggestedDates
  color?: 'purple' | 'red'
  onDelete?: () => void
}

const CalendarSelectedTimeSlot: FC<IProps> = ({ slot, color, onDelete }) => (
  <div className={cls([styles['slot'], styles[color || 'purple']])}>
    <div className={styles['slot-datetime']}>
      <div className={styles['slot-date']}>{dayjs(slot.start).format('DD.MM.YYYY')}</div>
      <div className={styles['slot-time']}>
        {dayjs(slot.start).format('LT')} - {dayjs(slot.end).format('LT')}
      </div>
    </div>

    {onDelete && (
      <button className={styles['delete-btn']}>
        <Icons className={styles['icon']} icon={'close'} onClick={() => onDelete()} />
      </button>
    )}
  </div>
)

export default CalendarSelectedTimeSlot
