@import 'assets/style/colors';

.slot {
  --slot-color: #{$black};
  --slot-border-color: #{transparentize($black, 0.5)};

  background-color: $white;
  border-radius: 6px;
  border: 1px solid var(--slot-border-color);
  padding: 10px 14px 10px 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  height: 56px;

  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 34px;
    border-radius: 6px;
    background-color: var(--slot-color);
  }

  &.purple {
    --slot-color: #{$purple};
    --slot-border-color: #{transparentize($purple, 0.5)};
  }

  &.red {
    --slot-color: #{$red};
    --slot-border-color: #{transparentize($red, 0.5)};
  }
}

.slot-datetime {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.slot-date {
  font-size: 14px;
  font-weight: 700;
  color: $dark-grey;
  text-align: left;
}

.slot-time {
  font-size: 12px;
  color: $dark-grey;
}

.delete-btn {
  all: unset;
  margin-left: auto;
  border: 1px solid $red;
  border-radius: 50%;
  height: 16px;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-style: solid;
    scale: 1.3;
  }

  &:active {
    border-style: solid;
  }
}

.icon {
  display: flex;

  svg {
    height: 10px;
    width: 10px;
  }
}
