@import 'assets/style/colors';

.block {
  --block-border-color: #{$black};
  --block-bg-color: #{transparentize($black, 0.8)};

  border: 1px solid var(--block-border-color);
  background-color: $white;
  border-radius: 14px;
  width: 360px;
  height: 340px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.pink {
    --block-border-color: #FF9DC3;
    --block-bg-color: #{transparentize(#FF9DC3, 0.8)};
  }

  &.blue {
    --block-border-color: #9C9CFF;
    --block-bg-color: #{transparentize(#9C9CFF, 0.8)};
  }
}

.header {
  background-color: var(--block-bg-color);
  border-bottom: 1px solid var(--block-border-color);
  padding: 10px 14px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.subtitle {
  color: $grey;
  font-size: 14px;
  line-height: 24px;
}

.task-title {
  color: $dark-grey;
  font-size: 16px;
  line-height: 26px;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  position: relative;
}

.requests-by-user {
  border-bottom: 1px solid $light-grey;
}
