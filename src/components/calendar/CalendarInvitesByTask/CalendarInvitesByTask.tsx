import React, { FC, useMemo } from 'react'
import { useSelector } from 'react-redux'
import cls from 'classnames'
import { TState } from 'store'
import { CalendarInvitesByUser } from '../CalendarInvitesByUser/CalendarInvitesByUser'
import { groupEventRequestsByAuthor, IGroupedEventsByTask } from 'helpers/groupEventRequests'

import styles from './CalendarInvitesByTask.module.scss'

export interface ICalendarInvitesByTaskProps {
  task: IGroupedEventsByTask['taskData']
  requests: IGroupedEventsByTask['array']
  rejectRequest: (requestId: number) => void
  approveRequest: (requestId: number, slotId: string) => void
  suggestAnotherTime: (requestId: number) => void
  themeColor?: 'pink' | 'blue'
  withAccordion?: boolean
}

const CalendarInvitesByTask: FC<ICalendarInvitesByTaskProps> = ({
  task,
  requests,
  rejectRequest,
  approveRequest,
  suggestAnotherTime,
  themeColor,
  withAccordion
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const groupedRequests = useMemo(() => groupEventRequestsByAuthor(requests as any[]), [requests])

  return (
    <div className={cls([styles['block'], styles[themeColor || 'pink']])}>
      <div className={styles['header']}>
        <span className={styles['subtitle']}>{words['user.subcontract.task']}:</span>
        <span className={styles['task-title']}>{task.title}</span>
      </div>
      <div className={styles['list']}>
        {Object.entries(groupedRequests).map(([key, groupRequests]) => (
          <CalendarInvitesByUser
            key={key}
            user={groupRequests.userData}
            requests={groupRequests.array}
            rejectRequest={rejectRequest}
            approveRequest={approveRequest}
            suggestAnotherTime={suggestAnotherTime}
            className={styles['requests-by-user']}
            withAccordion={withAccordion}
          />
        ))}
      </div>
    </div>
  )
}

export default CalendarInvitesByTask
