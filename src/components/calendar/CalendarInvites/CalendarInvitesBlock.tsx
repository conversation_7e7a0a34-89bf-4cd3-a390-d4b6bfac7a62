import React, { <PERSON> } from 'react'
import cls from 'classnames'
import { CalendarInvites, ICalendarInvitesProps } from './CalendarInvites'

import styles from './CalendarInvites.module.scss'

export const CalendarInvitesBlock: FC<ICalendarInvitesProps> = ({ className, ...props }) => {
  return (
    <CalendarInvites
      {...props}
      className={cls([styles['separate-block'], { [className || '']: !!className }])}
    />
  )
}
