@import 'assets/style/colors';

.separate-block {
  background-color: $white;
  border: 1px solid $light-grey;
  border-radius: 14px;
  width: 292px;
  height: fit-content;
}

.request-block {
  padding: 14px;
  display: flex;
  flex-direction: column;
}

.invite-marker {
  font-size: 12px;
  border-radius: 20px;
  background-color: $purple;
  color: $white;
  font-weight: 700;
  width: fit-content;
  padding: 4px 8px;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 10px 0 16px;
}

.btns-block {
  margin-top: auto;
  display: flex;
  gap: 12px;

  .btn {
    flex: 1;
    height: 28px;
    padding: 0;
    font-size: 14px;
  }
}

.anohter-time {
  color: $dark-grey;
  margin-top: 12px;
  font-size: 14px;
  line-height: 18px;
  font-weight: 600;
  padding: 0;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  width: fit-content;

  .icon {
    display: flex;
  }
}
