import React, { FC, useState } from 'react'
import { useSelector } from 'react-redux'
import cls from 'classnames'
import { TState } from 'store'
import { ICalendarEventRequest } from 'models/calendar-models'
import { But<PERSON> } from 'components/UiKit/Button'
import CalendarTimeSlot from 'components/calendar/CalendarTimeSlot/CalendarTimeSlot'
import { Icons } from 'components/Icons'

import styles from './CalendarInvites.module.scss'

export interface ICalendarInvitesProps {
  request: Pick<ICalendarEventRequest, 'id' | 'suggestedDates'>
  rejectRequest: (requestId: number) => void
  approveRequest: (requestId: number, slotId: string) => void
  suggestAnotherTime: (requestId: number) => void
  className?: string
}

export const CalendarInvites: FC<ICalendarInvitesProps> = ({
  request,
  rejectRequest,
  approveRequest,
  suggestAnotherTime,
  className
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null)

  const onCheck = (checked: boolean, slotId: string) => {
    setSelectedSlot(checked ? slotId : null)
  }

  return (
    <div key={request.id} className={cls([styles['request-block'], className || ''])}>
      <div className={styles['invite-marker']}>
        {words['user.subcontract.calendar.invite'] || 'Invite'} {request.id}
      </div>
      <div className={styles['list']}>
        {request.suggestedDates.map(slot => (
          <CalendarTimeSlot
            key={slot.slotId}
            slot={slot}
            checked={selectedSlot === slot.slotId}
            onCheck={value => onCheck(value, slot.slotId)}
          />
        ))}
      </div>
      <div className={styles['btns-block']}>
        <Button className={styles['btn']} outline onClick={() => rejectRequest(request.id)}>
          <span>{words['adminManager.dayOffTracker.buttons.reject']}</span>
        </Button>

        <Button
          className={styles['btn']}
          disabled={!selectedSlot}
          onClick={() => selectedSlot && approveRequest(request.id, selectedSlot)}
        >
          <span>{words['adminManager.dayOffTracker.buttons.approve']}</span>
        </Button>
      </div>
      <button className={styles['anohter-time']} onClick={() => suggestAnotherTime(request.id)}>
        {words['user.subcontract.suggestAnotherTime'] || 'Suggest another time'}
        <Icons icon="arrowRightGrey" className={styles['icon']} />
      </button>
    </div>
  )
}
