@import 'assets/style/colors';

.requests-block {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-block {
  display: flex;
  align-items: center;
  gap: 8px;

  .title {
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    margin: 0;
  }

  .amount {
    background-color: $green;
    color: $white;
    border-radius: 50%;
    font-size: 12px;
    line-height: 14px;
    aspect-ratio: 1;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.control-block {
  display: flex;
  gap: 14px;
}

.control-btn {
  background-color: $blue;
  border-radius: 50%;
  height: 26px;
  width: 26px;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  &:not(:disabled) {
    cursor: pointer;
  }

  &:disabled {
    background-color: $grey-disabled;
  }

  &.left .icon {
    transform: rotateZ(90deg);
  }
  &.right .icon {
    transform: rotateZ(-90deg);
  }

  .icon svg path {
    stroke: $white;
  }
}

.list {
  margin: 0;
}
