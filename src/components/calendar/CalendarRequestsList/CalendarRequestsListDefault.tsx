import React, { FC, useMemo } from 'react'
import { ICalendarEventRequest } from 'models/calendar-models'
import { CalendarRequestsList } from './CalendarRequestsList'
import { ICalendarInvitesProps } from '../CalendarInvites/CalendarInvites'
import { CalendarInvitesBlock } from '../CalendarInvites/CalendarInvitesBlock'

interface ICalendarRequestsListDefaultProps
  extends Pick<ICalendarInvitesProps, 'rejectRequest' | 'approveRequest' | 'suggestAnotherTime'> {
  title: string
  requests: ICalendarEventRequest[]
}

export const CalendarRequestsListDefault: FC<ICalendarRequestsListDefaultProps> = props => {
  const { title, requests, ...rest } = props

  const slideProps = useMemo(
    () => requests.map(request => ({ htmlKey: request.id, request, ...rest })),
    [requests]
  )

  return (
    <CalendarRequestsList<ICalendarInvitesProps>
      title={title}
      amount={requests.length}
      slide={CalendarInvitesBlock}
      slideProps={slideProps}
    />
  )
}
