import React, { FC, useMemo } from 'react'
import { ICalendarEventRequest } from 'models/calendar-models'
import { CalendarRequestsList } from './CalendarRequestsList'
import { groupEventRequestsByAuthor } from 'helpers/groupEventRequests'
import { ICalendarInvitesByUserProps } from '../CalendarInvitesByUser/CalendarInvitesByUser'
import { CalendarInvitesByUserBlock } from '../CalendarInvitesByUser/CalendarInvitesByUserBlock'

interface ICalendarRequestsListByUserProps
  extends Pick<
    ICalendarInvitesByUserProps,
    'rejectRequest' | 'approveRequest' | 'suggestAnotherTime'
  > {
  title: string
  requests: ICalendarEventRequest[]
}

export const CalendarRequestsListByUser: FC<ICalendarRequestsListByUserProps> = props => {
  const { title, requests, ...rest } = props

  const slideProps = useMemo(
    () =>
      Object.entries(groupEventRequestsByAuthor(requests)).map(([key, groupRequests]) => ({
        htmlKey: key,
        user: groupRequests.userData,
        requests: groupRequests.array,
        ...rest
      })),
    [requests]
  )

  return (
    <CalendarRequestsList<ICalendarInvitesByUserProps>
      title={title}
      amount={requests.length}
      slide={CalendarInvitesByUserBlock}
      slideProps={slideProps}
    />
  )
}
