import React, { useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation } from 'swiper/modules'
import cls from 'classnames'
import { Icons } from 'components/Icons'

import 'swiper/css'
import 'swiper/css/navigation'
import styles from './CalendarRequestsList.module.scss'

interface ICalendarRequestsListProps<T> {
  title: string
  amount: number
  slide: React.FC<T>
  slideProps: (T & { htmlKey: number | string })[]
}

// prettier-ignore
export const CalendarRequestsList = <T,>(props: ICalendarRequestsListProps<T>) => {
  const { title, amount, slide: Slide, slideProps } = props

  const prevRef = useRef(null);
  const nextRef = useRef(null);

  return (
    <div className={styles['requests-block']}>
      <div className={styles['header']}>
        <div className={styles['title-block']}>
          <h3 className={styles['title']}>{title}</h3>
          <span className={styles['amount']}>{amount}</span>
        </div>
        <div className={styles['control-block']}>
          <button ref={prevRef} className={cls([styles['control-btn'], styles['left']])}>
            <Icons className={styles['icon']} icon="arrowDown" />
          </button>
          <button ref={nextRef} className={cls([styles['control-btn'], styles['right']])}>
            <Icons className={styles['icon']} icon="arrowDown" />
          </button>
        </div>
      </div>
      <Swiper 
        spaceBetween={20} 
        slidesPerView="auto" 
        modules={[Navigation]} 
        className={styles['list']} 
        navigation={{
          prevEl: prevRef.current,
          nextEl: nextRef.current,
        }}
        onBeforeInit={(swiper: any) => {
          swiper.params.navigation.prevEl = prevRef.current;
          swiper.params.navigation.nextEl = nextRef.current;
          swiper.navigation.init();
          swiper.navigation.update();
        }}
      >
        {slideProps.map(({ htmlKey, ...rest }) => (
          <SwiperSlide key={htmlKey} style={{ width: 'fit-content' }}>
            <Slide {...rest as any} />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}
