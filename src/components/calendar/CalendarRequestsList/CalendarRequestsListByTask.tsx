import React, { FC, useMemo } from 'react'
import { ICalendarEventRequest } from 'models/calendar-models'
import { CalendarRequestsList } from './CalendarRequestsList'
import { groupEventRequestsByTask } from 'helpers/groupEventRequests'
import CalendarInvitesByTask, {
  ICalendarInvitesByTaskProps
} from '../CalendarInvitesByTask/CalendarInvitesByTask'

interface ICalendarRequestsListByTaskProps
  extends Pick<
    ICalendarInvitesByTaskProps,
    'rejectRequest' | 'approveRequest' | 'suggestAnotherTime' | 'themeColor' | 'withAccordion'
  > {
  title: string
  requests: ICalendarEventRequest[]
}

export const CalendarRequestsListByTask: FC<ICalendarRequestsListByTaskProps> = props => {
  const { title, requests, ...rest } = props

  const slideProps = useMemo(
    () =>
      Object.entries(groupEventRequestsByTask(requests)).map(([key, groupRequests]) => ({
        htmlKey: key,
        task: groupRequests.taskData,
        requests: groupRequests.array,
        ...rest
      })),
    [requests]
  )

  return (
    <CalendarRequestsList<ICalendarInvitesByTaskProps>
      title={title}
      amount={requests.length}
      slide={CalendarInvitesByTask}
      slideProps={slideProps}
    />
  )
}
