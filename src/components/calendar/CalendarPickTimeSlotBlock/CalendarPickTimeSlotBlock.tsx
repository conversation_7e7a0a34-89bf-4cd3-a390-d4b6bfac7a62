import React, { FC } from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { config } from 'globalConfigs'
import { useAvailableFunds } from 'hooks/useAvailableFunds'
import { ISuggestedDates } from 'models/calendar-models'
import CalendarSelectedTimeSlot from '../CalendarSelectedTimeSlot/CalendarSelectedTimeSlot'
import { Button } from 'components/UiKit/Button'

import styles from './CalendarPickTimeSlotBlock.module.scss'

export interface IPickTimeSlotBlockProps {
  slots: ISuggestedDates[]
  deleteSlot: (slotId: ISuggestedDates['slotId']) => void
  onSubmit: () => void
  onCancel?: () => void
  buttonText: string
}

const CalendarPickTimeSlotBlock: FC<IPickTimeSlotBlockProps> = ({
  slots,
  deleteSlot,
  buttonText,
  onCancel,
  onSubmit
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const { availableCalls } = useAvailableFunds()

  return (
    <div className={styles['block']}>
      <div className={styles['header']}>
        <h3 className={styles['subtitle']}>
          {words['user.subcontract.calendar.selectedTime'] || 'Selected time'}
        </h3>
        <p className={styles['selected']}>
          {(words['user.subcontract.calendar.selectedValues'] || 'Selected <VALUE1> of <VALUE2>')
            .replace('<VALUE1>', slots.length)
            .replace('<VALUE2>', config.timeSlotsLimit)}
        </p>
      </div>
      <div className={styles['list']}>
        {slots.map(slot => (
          <CalendarSelectedTimeSlot
            key={slot.start.toString()}
            slot={slot}
            onDelete={() => deleteSlot(slot.slotId)}
          />
        ))}
        {new Array(config.timeSlotsLimit - slots.length).fill(slots.length).map((length, idx) => (
          <div key={length + idx} className={styles['empty-slot']}>
            {words['user.subcontract.calendar.selectTime'] || 'Select time in calendar'}
          </div>
        ))}
      </div>

      <div className={styles['btn-block']}>
        {onCancel && (
          <Button onClick={onCancel} outline size={'md'} className={styles['btn']}>
            <span>{words['user.subcontract.cancel']}</span>
          </Button>
        )}
        <Button
          onClick={onSubmit}
          size={'md'}
          disabled={!slots.length || !availableCalls}
          className={styles['btn']}
        >
          <span>{buttonText}</span>
        </Button>
      </div>
    </div>
  )
}

export default CalendarPickTimeSlotBlock
