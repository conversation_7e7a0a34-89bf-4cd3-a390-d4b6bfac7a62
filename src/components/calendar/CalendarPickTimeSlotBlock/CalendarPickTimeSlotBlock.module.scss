@import 'assets/style/colors';
@import 'assets/style/variables';

.block {
  display: flex;
  flex-direction: column;
  gap: 14px;

  .btn {
    flex: 1;
    height: 32px;
    padding: 0;
  }
}

.header {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.subtitle {
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  margin: 0;
}

.selected {
  color: $grey;
  font-size: 14px;
  line-height: 18px;
  margin: 0;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.empty-slot {
  height: 56px;
  background-color: $white;
  border: 1px dashed $light-grey;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: $grey-disabled;
  font-size: 12px;
}

.btn-block {
  display: flex;
  gap: 12px;
}
