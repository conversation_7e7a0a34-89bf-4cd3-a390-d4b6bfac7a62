import React, { FC } from 'react'
import { useSelector } from 'react-redux'
import cls from 'classnames'
import { TState } from 'store'

import styles from './CalendarEventsFilter.module.scss'

export interface IEventFilterCheckbox {
  labelKey: string
  checked: boolean
  setNewChecked: (value: boolean) => void
  color?: 'purple' | 'blue' | 'orange' | 'pink'
  type?: 'transparent'
}

interface ICalendarEventsFilterProps {
  checkboxes: IEventFilterCheckbox[][]
}

export const CalendarEventsFilter: FC<ICalendarEventsFilterProps> = ({ checkboxes }) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <div className={styles['events-filter']}>
      <div className={styles['title']}>
        {words['user.subcontract.calendar.filter.title'] || 'My calendars:'}
      </div>
      {checkboxes.map((checkboxList, idx) => (
        <div key={idx} className={styles['list']}>
          {checkboxList.map(checkbox => (
            <label
              key={checkbox.labelKey}
              className={cls(
                styles['checkbox'],
                styles[checkbox.color || ''],
                styles[checkbox.type || '']
              )}
            >
              <input
                type="checkbox"
                checked={checkbox.checked}
                onChange={e => checkbox.setNewChecked(e.target.checked)}
              />
              {/* TODO: remove second value */}
              <span>{words[checkbox.labelKey] || checkbox.labelKey}</span>
            </label>
          ))}
        </div>
      ))}
    </div>
  )
}
