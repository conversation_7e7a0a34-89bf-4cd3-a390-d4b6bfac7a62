@import 'assets/style/colors';

$padding-size: 14px;

.events-filter {
  background-color: $white;
  border: 1px solid $light-grey;
  padding: $padding-size;
  border-radius: 14px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    &:not(:last-child) {
      padding-bottom: 10px;
      position: relative;

      &::after {
        content: '';
        background-color: $light-grey;
        width: calc(100% + #{$padding-size} * 2);
        height: 1px;
        position: absolute;
        margin-top: 10px;
        bottom: 0;
        left: calc(-1 * #{$padding-size});
      }
    }
  }

  .checkbox {
    --theme-color: #{$black};

    height: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    line-height: 18px;
    width: fit-content;
    position: relative;
    cursor: pointer;
    user-select: none;

    input {
      height: 0;
      width: 0;
      opacity: 0;
      visibility: hidden;
      position: absolute;
    }

    &::before {
      content: '';
      height: 16px;
      width: 16px;
      border-radius: 4px;
      background-color: transparent;
      border: 1px solid var(--theme-color);
    }

    &:has(input:checked)::before {
      background-color: var(--theme-color);
      background-image: url('../../../assets/images/checked-mark-icon-white.svg');
    }
    &.transparent:has(input:checked)::before {
      background-color: transparent;
      background-image: url('../../../assets/images/checked-mark-icon-black.svg');
    }

    &.purple {
      --theme-color: #{$purple};
    }

    &.blue {
      --theme-color: #{$blue};
    }

    &.orange {
      --theme-color: #{$orange};
    }
    
    &.pink {
      --theme-color: #{$pink};
    }
  }
}
