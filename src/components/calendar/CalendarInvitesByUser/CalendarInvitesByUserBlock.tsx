import React, { <PERSON> } from 'react'
import cls from 'classnames'
import { CalendarInvitesByUser, ICalendarInvitesByUserProps } from './CalendarInvitesByUser'

import styles from './CalendarInvitesByUser.module.scss'

export const CalendarInvitesByUserBlock: FC<ICalendarInvitesByUserProps> = ({
  className,
  ...props
}) => {
  return (
    <CalendarInvitesByUser
      {...props}
      className={cls([styles['separate-block'], { [className || '']: !!className }])}
    />
  )
}
