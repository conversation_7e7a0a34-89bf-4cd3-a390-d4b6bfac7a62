import React, { FC, useState } from 'react'
import cls from 'classnames'
import { IGroupedEventsByAuthor } from 'helpers/groupEventRequests'
import { CalendarInvites } from 'components/calendar/CalendarInvites/CalendarInvites'
import { UserAvatar } from 'components/UserAvatar'
import { Icons } from 'components/Icons'

import styles from './CalendarInvitesByUser.module.scss'

export interface ICalendarInvitesByUserProps {
  className?: string
  user: IGroupedEventsByAuthor['userData']
  requests: IGroupedEventsByAuthor['array']
  rejectRequest: (requestId: number) => void
  approveRequest: (requestId: number, slotId: string) => void
  suggestAnotherTime: (requestId: number) => void
  withAccordion?: boolean
}

export const CalendarInvitesByUser: FC<ICalendarInvitesByUserProps> = ({
  className,
  user,
  requests,
  rejectRequest,
  approveRequest,
  suggestAnotherTime,
  withAccordion
}) => {
  const [isOpened, setIsOpened] = useState(false)

  return (
    <div className={cls([styles['block'], { [className || '']: !!className }])}>
      <div className={styles['header']}>
        <div className={styles['user']}>
          <UserAvatar className={styles['avatar']} photo={user ? user.photo : undefined} />
          <p className={styles['username']}>{user ? user.fullName : 'UNKNOWN'}</p>
        </div>
        <div className={styles['btn-block']}>
          <div className={styles['amount']}>{requests.length}</div>
          {withAccordion && (
            <Icons
              icon="arrowDown"
              className={cls([styles['accordion-btn'], { [styles['is-open']]: isOpened }])}
              onClick={() => setIsOpened(prev => !prev)}
            />
          )}
        </div>
      </div>
      {(!withAccordion || isOpened) &&
        requests.map(request => (
          <CalendarInvites
            key={request.id}
            request={request}
            rejectRequest={rejectRequest}
            approveRequest={approveRequest}
            suggestAnotherTime={suggestAnotherTime}
            className={styles['invite-details']}
          />
        ))}
    </div>
  )
}
