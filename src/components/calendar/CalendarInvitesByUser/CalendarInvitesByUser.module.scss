@import 'assets/style/colors';

.separate-block {
  border-radius: 14px;
  border: 1px solid $light-grey;
  width: 292px;
  height: fit-content;
}

.block {
  background-color: $white;
}

.header {
  padding: 10px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.user {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 228px;
}

.avatar {
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 50%;
}

.username {
  font-size: 14px;
  line-height: 18px;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btn-block {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background-color: transparentize($purple, 0.75);
  color: $purple;
  font-size: 12px;
  font-weight: 700;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.accordion-btn {
  display: flex;
  transition: transform .3s ease;
  cursor: pointer;

  &.is-open {
    transform: rotateX(180deg);
  }
}

.invite-details {
  border-top: 1px solid $light-grey;
  padding-top: 10px;
}
