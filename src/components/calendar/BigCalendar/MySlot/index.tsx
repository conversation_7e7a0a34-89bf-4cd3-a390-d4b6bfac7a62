import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import dayjs from 'utils/dayjs'
import { IEvent } from '../BigCalender.model'

import styles from './MySlot.module.scss'

export const MySlot = ({ event }: { event: { event: IEvent; title: string } }) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const eventStart = dayjs(event.event.start)
    .utc()
    .local()
    .format('LT')
  const eventEnd = dayjs(event.event.end)
    .utc()
    .local()
    .format('LT')

  return (
    <div className={styles.slotBody}>
      <div className={styles.slotTitle}>{event.title}</div>
      <div className={styles.slotTime}>
        {eventStart} - {eventEnd}
      </div>
      {event.event.withUser && (
        <div className={styles.slotWithUser}>
          {words['user.subcontract.calendar.withUser'] || 'With:'}
          <b>{event.event.withUser}</b>
        </div>
      )}
    </div>
  )
}
