@import 'assets/style/colors';
@import 'assets/style/variables';
@import 'assets/style/mixins';

.container {
  border: 1px solid #EBEBEB;
  border-radius: 10px;
  flex-grow: 1;
}

.modal,
.warnModal {
  position: fixed;
  z-index: 1001;
  background-color: #FFFFFF;
  left: 50%;
  transform: translate(-50%, 0);
  width: 616px;
  height: fit-content;
  top: 0;
  bottom: 0;
  margin: auto 0;
  border-radius: 20px;
  padding: 31px 24px 24px 24px;
  
  h2 {
    margin: 0px;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
    color: $dark-grey;
  }
  .title, .description {
    text-align: left;
    width: 100%;
    margin-bottom: 15px;
  }
  .modalLine {
    border: 1px solid $light-grey
  }

  .timeSlot {
    display: flex;
    flex-direction: column;
    margin-bottom: 29px;
    margin-top: 20px;
  }

  p {
    margin: 0px;
    color: $dark-grey;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
  }
}
.warnModal {
  box-shadow: 0 0 20px $box-shadow;
}
.warnDescription {
  padding-top: 20px;
  padding-bottom: 20px;
}

.mask {
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  background: #00000090;
  z-index: 1000;
  overflow: hidden;
}

.days {
  display: flex;
  flex-direction: column;
  .day-week {
    color: #9A9A9A !important;
    font-weight: 400 !important;
    font-size: 10px !important;
    line-height: 14px !important;
  }

  .day-month {
    color: #3737ED !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    line-height: 24px !important;
  }

  .today {
    border-radius: 100%;
    background-color: #3737ED;

    .day-month {
      color: #fff !important;
    }
  }
}

.days > div {
  display: flex;
  justify-content: center;
  width: 28px;
  height: 28px;
  align-items: center;
}

.timeText {
  display: flex;
  align-items: center;

  p {
    left: 0;
    padding-top: 0;
  }

  i {
    display: flex;
  }
}

.buttons {
  margin-top: 30px;
  display: flex;
  align-items: center;
  justify-content: space-around;

  button {
    cursor: pointer;
    min-width: 200px;
  }
}

.modalIcons {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 24px;
}

.modalButton {
  transition: all 0.3s;
  cursor: pointer;

  i {
    svg {
      width: 24px;
      height: 24px;
    }
  }
}

.modalButton:hover {
  scale: 1.2;
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 24px 0;
}

.modalText {
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  text-align: left;

  span {
    color: $blue;
    font-weight: 700;
  }
}

.modalTitle {
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}

.modalDescription {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;

  span {
    display: block;
  }
}

.validationError {
  color: $invalid-red;
  display: block;
  margin: 10px 0;
}

.checkboxContainer {
  display: flex;
  align-items: center;
}
.meetLink {
  margin-bottom: 20px;
  text-align: start;
}
