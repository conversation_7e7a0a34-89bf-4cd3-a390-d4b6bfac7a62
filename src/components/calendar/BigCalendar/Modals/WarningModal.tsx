import React from 'react'

import { BaseButton } from 'components/UiKit/Button/BaseButton'
import { Icons } from 'components/Icons'
import styles from '../BigCalendar.module.scss'
import { IWarningModalProp } from '../BigCalender.model'

export const WarningModal = ({
  closeWarnModal,
  handleAction,
  content,
  actionBtnName,
  cancelBtnName
}: IWarningModalProp): JSX.Element => (
  <div className={styles.warnModal}>
    <div className={styles.modalIcons}>
      <div />
      <Icons icon="closeModal" onClick={closeWarnModal} className={styles.modalButton} />
    </div>
    <div className={styles.warnDescription}>{content}</div>
    <div className={styles.buttons}>
      <BaseButton
        children={cancelBtnName}
        type="button"
        outline={true}
        className={styles['btn-outline']}
        onClick={closeWarnModal}
      />
      <BaseButton
        className={styles.btn}
        children={actionBtnName}
        size="lgs"
        type="button"
        onClick={handleAction}
      />
    </div>
  </div>
)
