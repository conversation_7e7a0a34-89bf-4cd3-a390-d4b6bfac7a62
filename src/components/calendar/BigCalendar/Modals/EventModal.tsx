import React, { useState } from 'react'

// import { BaseButton } from 'components/UiKit/Button/BaseButton'
// import Checkbox from 'components/UiKit/Checkbox/Checkbox'
import { Icons } from 'components/Icons'
import styles from '../BigCalendar.module.scss'
import { EventModalProps } from '../BigCalender.model'
import dayjs from 'utils/dayjs'
import { WarningModal } from './WarningModal'
import { useSelector } from 'react-redux'
import { TState } from 'store'

export const EventModal = ({
  visible,
  event,
  // editMode,
  // setEditMode,
  setEventModal,
  // onCreateEvent,
  // onEditEvent,
  handleCancelCreateEvent,
  userId,
  // isForeignEventWarn,
  // setForeignEventWarn,
  onDeleteEventGC,
  onDeleteInviteReqSlot
}: EventModalProps): JSX.Element => {
  const words = useSelector((state: TState) => state.global.language.words)
  const [warnModal, setWarnModal] = useState(false)

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1)
  }

  const handleDeleteEvent = () => {
    if (!event) return

    if (event.eventId) {
      onDeleteEventGC(event.eventId)
      setWarnModal(false)
      return
    }

    if (event.id && event.slotId) {
      const id = event.id.split('_')[0]
      onDeleteInviteReqSlot(+id, event.slotId)
      setWarnModal(false)
    }
  }

  const closeWarnModal = () => {
    setWarnModal(false)
  }

  const closeAllModal = () => {
    handleCancelCreateEvent(false)
    setWarnModal(false)
  }

  // const closeForeignEventWarn = () => {
  //   setForeignEventWarn(false)
  //   setEventModal(false)
  // }

  if (!visible || !event) return <></>

  return (
    <>
      {!warnModal && (
        // {!warnModal && !isForeignEventWarn && (
        <div className={styles.modal}>
          <>
            <div className={styles.modalIcons}>
              {(event.userId && +event.userId === userId) ||
              (event.authorId && +event.authorId === userId) ? (
                <Icons
                  icon="delete"
                  onClick={() => setWarnModal(true)}
                  className={`${styles.modalButton}`}
                />
              ) : (
                <div />
              )}
              <Icons
                className={`${styles.modalButton}`}
                icon="closeModal"
                onClick={() => setEventModal(false)}
              />
            </div>
            <h2>{words['user.subcontract.task']}</h2>
            <div className={styles.modalContent}>
              <p className={`${styles.modalText} ${styles.modalTitle}`}>
                <span>{words['admin.productionCalendar.new.name']}:</span> {event.title}
              </p>
              <p className={`${styles.modalText} ${styles.modalDescription}`}>
                <span>{words['user.subcontract.task.description']}:</span> {event.description}
              </p>
              {event.withUser && (
                <p className={`${styles.modalText}`}>
                  <span>{words['user.subcontract.calendar.withUser'] || 'With:'}</span>{' '}
                  {event.withUser}
                </p>
              )}
            </div>

            {event.hangoutLink && (
              <div className={styles.meetLink}>
                <a href={event.hangoutLink} target="_blanc">
                  Google meet
                </a>
              </div>
            )}

            <hr className={styles.modalLine} />
            <div className={styles.timeSlot}>
              <div className={styles.timeText}>
                <Icons icon="timeIcon" />
                <p style={{ marginRight: '8px' }}>
                  {capitalizeFirstLetter(dayjs(event.start).format('dddd, D MMMM'))}
                </p>
                <p>
                  {`${dayjs(event.start).format('LT')}`}
                  {' - '}
                  {dayjs(event.end).format('LT')}
                </p>
              </div>
            </div>
          </>
          {/* ) : ( */}
          {/* <>
              <h2>{words['user.subcontract.task']}</h2>
              <Form
                initialValues={event}
                onSubmit={async (values: INewEvent) => {
                  if (values.id) {
                    const dirtyFields = dirtyFieldsRef.current
                    onEditEvent(values, dirtyFields)
                    return
                  }

                  onCreateEvent(values)
                }}
              >
                {({ form, handleSubmit }) => {
                  const formState = form.getState()
                  const formValues = formState.values
                  const { dirty, dirtyFields } = formState
                  dirtyFieldsRef.current = dirtyFields

                  const onCheck = (e: any) => {
                    form.change('meet', e.target.checked)
                  }
                  return (
                    <form onSubmit={handleSubmit}>
                      <FieldFactory
                        form={form}
                        config={[
                          {
                            items: [
                              {
                                name: 'title',
                                label: words['admin.productionCalendar.new.name'],
                                required: true,
                                maxLength: 510,
                                inputWrapperClassName: styles['title'],
                                component: () => {
                                  return {
                                    type: 'input',
                                    props: {
                                      variant: 'outlined'
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        ]}
                        words={words}
                      />
                      <FieldFactory
                        form={form}
                        config={[
                          {
                            style: {},
                            items: [
                              {
                                name: 'description',
                                label: words['user.subcontract.task.description'],
                                required: true,
                                maxLength: 8000,
                                inputWrapperClassName: styles['description'],
                                component: () => {
                                  return {
                                    type: 'textarea',
                                    props: { size: 'sm' }
                                  }
                                }
                              }
                            ]
                          }
                        ]}
                        words={words}
                      />

                      <div className={styles.checkboxContainer}>
                        <Checkbox
                          name="meet"
                          checked={formValues.meet}
                          onChange={(e: any) => onCheck(e)}
                          disabled={!!event.id}
                          label={words['user.subcontract.createMeet'] || 'Create Google Meet'}
                        />
                      </div>

                      <hr className={styles.modalLine} />
                      <div className={styles.timeSlot}>
                        <div className={styles.timeText}>
                          <Icons icon="timeIcon" />
                          <p style={{ marginRight: '8px' }}>
                            {capitalizeFirstLetter(dayjs(event.start).format('dddd, D MMMM'))}
                          </p>
                          <p>
                            {`${dayjs(event.start).format('LT')}`}
                            {' - '}
                            {dayjs(event.end).format('LT')}
                          </p>
                        </div>
                        <div className={styles.buttons}>
                          <BaseButton
                            children={words['user.subcontract.cancel']}
                            type="button"
                            outline={true}
                            className={styles['btn-outline']}
                            onClick={handleCancelCreateEvent}
                          />
                          <BaseButton
                            className={styles.btn}
                            children={words['admin.users.table.timeEdit.Save']}
                            size="lgs"
                            type="submit"
                            disabled={!dirty}
                          />
                        </div>
                      </div>
                    </form>
                  )
                }}
              </Form>
            </> */}
        </div>
      )}

      {warnModal && (
        // {warnModal && !isForeignEventWarn && (
        <WarningModal
          closeWarnModal={closeWarnModal}
          handleAction={handleDeleteEvent}
          content={words['user.subcontract.areYouSure'] || 'Are you sure you want to delete?'}
          cancelBtnName={words['user.subcontract.cancel']}
          actionBtnName={words['user.subcontract.delete'] || 'Delete'}
        />
      )}

      {/* {isForeignEventWarn && (
        <WarningModal
          closeWarnModal={closeForeignEventWarn}
          handleAction={closeForeignEventWarn}
          content={words['user.subcontract.noPermissions'] || 'You have no permissions.'}
          cancelBtnName={words['user.subcontract.cancel']}
          actionBtnName="Ok"
        />
      )} */}

      {visible && <div className={styles.mask} onClick={closeAllModal} />}
    </>
  )
}
