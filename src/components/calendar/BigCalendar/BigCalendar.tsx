import React, { useState, FC, useEffect, useCallback, useMemo } from 'react'
import { useSelector } from 'react-redux'
import cls from 'classnames'
import { Calendar, momentLocalizer, SlotInfo } from 'react-big-calendar'
import format from 'date-fns/format'
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop'

import './styles.scss'
import { TState } from 'store'
import styles from './BigCalendar.module.scss'
import dayjs from 'utils/dayjs'
import moment from 'utils/moment'
import { IBigCalendarsProps, IEvent, ISelectedEvent } from './BigCalender.model'
import { MySlot } from './MySlot'
import { calculateRange } from 'helpers/helpers'
import { EventModal } from './Modals/EventModal'
import { EEventRequestType } from 'models/calendar-models'

const localizer = momentLocalizer(moment)
const DnDCalendar = withDragAndDrop(Calendar)

const BigCalendar: FC<IBigCalendarsProps> = ({
  events,
  currentDate,
  isEventModal,
  setEventModal,
  loadEvents,
  onDeleteEventGC,
  onDeleteInviteReqSlot,
  selectedSlots = [],
  setSelectedSlots = () => undefined
}) => {
  const userId = useSelector((state: TState) => state.auth.data.id)
  const initForm = {
    title: '',
    description: ''
  }

  // const [isForeignEventWarn, setForeignEventWarn] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<Partial<ISelectedEvent>>({
    ...initForm
  })
  const [range, setRange] = useState<{ timeMin: Date; timeMax: Date } | null>(null)

  useEffect(() => {
    const currentRange = calculateRange(currentDate, 'week')

    setRange(currentRange)
    if (range) {
      const isSame =
        dayjs(range.timeMin).isSame(currentRange.timeMin) &&
        dayjs(range.timeMax).isSame(currentRange.timeMax)

      if (isSame) return
    }

    loadEvents({
      timeMin: currentRange.timeMin.toString(),
      timeMax: currentRange.timeMax.toString()
    })
  }, [currentDate])

  const onEventResize = useCallback(
    ({ event, start, end }: any) => {
      // If the event is created but not saved in the database
      if (event.type === EEventRequestType.REQUESTED && event.slotId) {
        const newItems = selectedSlots.map(item =>
          item.slotId === event.slotId ? { ...item, start, end } : item
        )
        setSelectedSlots(newItems)
      }
    },
    [selectedSlots]
  )

  const onSelectEvent = useCallback((data: any) => {
    if (data.eventId || data.id) {
      setSelectedEvent(data)
      setEventModal(true)
    }
  }, [])

  const onSelectSlot = useCallback(
    (slotInfo: SlotInfo) => {
      const slotId = Date.now().toString()
      const newSlot = { ...slotInfo, type: EEventRequestType.REQUESTED, slotId }
      setSelectedSlots([...selectedSlots, newSlot])
    },
    [selectedSlots]
  )

  const handleCancelCreateEvent = () => {
    setSelectedEvent({ ...initForm })
    setEventModal(false)
  }

  const calendarComponents: any = useMemo(
    () => ({
      week: {
        header: ({ date }: any) => {
          const dayOfWeek = format(date, 'EE')
          const dayOfMonth = format(date, 'd')
          return (
            <div className={styles.days}>
              <span className={styles['day-week']}>{dayOfWeek}</span>
              <div className={cls({ [styles.today]: new Date().getDate() === +dayOfMonth })}>
                <span className={styles['day-month']}>{dayOfMonth}</span>
              </div>
            </div>
          )
        }
      },
      timeGutter: ({ components }: any) => {
        const TimeGutterComponent = components.timeGutter
        return (
          <TimeGutterComponent
            {...TimeGutterComponent.props}
            ref={TimeGutterComponent.gutterRef}
            className="rbc-time-gutter"
          />
        )
      },
      event: (event: { event: IEvent; title: string }) => <MySlot event={event} />
    }),
    [events, currentDate]
  )

  const calendarFormats: any = useMemo(
    () => ({
      timeGutterFormat: (date: any, culture: any) => format(date, 'h a', culture),
      eventTimeRangeFormat: () => null
    }),
    [events, currentDate]
  )

  return (
    <div className={styles.container}>
      <DnDCalendar
        formats={calendarFormats}
        components={calendarComponents}
        date={currentDate}
        startAccessor={(event: any) => event.start}
        endAccessor={(event: any) => event.end}
        onNavigate={(event: any) => event.start}
        eventPropGetter={(event: any) => {
          const classNames: string[] = []
          switch (true) {
            case event.isNewEvent:
              classNames.push('new-event')
              break
            case event.isForeignEvent:
              classNames.push('orange-event')
              break
            case event.isGeneralEvent:
              classNames.push('purple-event')
              break
            case event.isMyEvent:
            case event.isUser:
              classNames.push('blue-event')
              break
            case event.isCustomer:
              classNames.push('pink-event')
              break
          }

          if (
            (event.isGeneralEvent || event.isMyEvent || event.isUser || event.isCustomer) &&
            event.type === EEventRequestType.REQUESTED
          )
            classNames.push('request')
          if (event.isOverlap) classNames.push('overlap')

          return {
            className: classNames.join(' ')
          }
        }}
        toolbar={false}
        defaultView="week"
        events={[...events, ...selectedSlots.map(slot => ({ ...slot, isNewEvent: true }))]}
        localizer={localizer}
        onEventDrop={onEventResize}
        onEventResize={onEventResize}
        scrollToTime={new Date()}
        resizable
        selectable
        onSelectEvent={onSelectEvent}
        onSelectSlot={onSelectSlot}
        step={15}
        timeslots={4}
        style={{ height: 'calc(100vh - 80px)', width: '100%' }}
      />
      <EventModal
        visible={isEventModal}
        event={selectedEvent}
        setEventModal={setEventModal}
        userId={userId}
        handleCancelCreateEvent={handleCancelCreateEvent}
        // isForeignEventWarn={isForeignEventWarn}
        // setForeignEventWarn={setForeignEventWarn}
        onDeleteEventGC={onDeleteEventGC}
        onDeleteInviteReqSlot={onDeleteInviteReqSlot}
      />
    </div>
  )
}

export default BigCalendar
