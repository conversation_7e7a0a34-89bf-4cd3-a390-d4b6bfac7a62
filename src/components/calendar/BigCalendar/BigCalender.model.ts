import { ISelectedSlot } from 'models/calendar-models'
import { SlotInfo } from 'react-big-calendar'
import { IGetUserEventsParam } from 'screens/client/InterviewInvitation/InterviewInvitation.model'

export interface IEvent {
  pretenderId: number
  start: string
  end: string
  title: string
  description: string
  isExecutor: boolean
  sourceResource?: null
  isOverlap: boolean
  eventId?: string
  hangoutLink?: string
  withUser?: string
}

export interface IBigCalendarsProps {
  events: IEvent[]
  currentDate: any
  isEventModal: boolean
  setEventModal: (v: boolean) => void
  loadEvents: (range: Pick<IGetUserEventsParam, 'timeMin' | 'timeMax'>) => void
  onDeleteEventGC: (eventId: string) => void
  onDeleteInviteReqSlot: (requestId: number, slotId: string) => void
  taskId?: number
  selectedSlots?: ISelectedSlot[]
  setSelectedSlots?: (array: ISelectedSlot[]) => void
}

export interface INewEvent {
  title: string
  description: string
  id?: string
}

export interface ISelectedEvent extends INewEvent, SlotInfo {
  taskId?: number
  userId?: string
  executorId?: string
  hangoutLink?: string
  eventId?: string
  authorId?: number
  invitedId?: number
  slotId?: string
  withUser?: string
}

export interface IWarningModalProp {
  closeWarnModal: any
  handleAction: any
  content: string
  actionBtnName: string
  cancelBtnName: string
}

export interface EventModalProps {
  visible: boolean
  event: Partial<ISelectedEvent> | null
  userId: number
  setEventModal: (v: boolean) => void
  // setForeignEventWarn: (v: boolean) => void
  // isForeignEventWarn: boolean
  handleCancelCreateEvent: any
  onDeleteEventGC: (id: string) => void
  onDeleteInviteReqSlot: (recordId: number, slotId: string) => void
}
