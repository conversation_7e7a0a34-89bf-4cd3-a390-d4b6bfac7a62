import React, { <PERSON> } from 'react'
import { Field } from 'react-final-form'
import styles from './Checkbox.module.scss'

type TCheckboxProps = {
  name?: any
  value?: string
}
const Checkbox: FC<TCheckboxProps> = ({ name, value }) => {
  return (
    <div className={styles.wrapper}>
      <Field
        name={name}
        component="input"
        type="checkbox"
        value={true}
        className={styles.checkbox}
        id={name}
      />
      <label className="custom-control-label" htmlFor={name}>
        {value}
      </label>
    </div>
  )
}

export default Checkbox
