@import '../../assets/style/colors';

.element__container {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}
.element__date {
  width: fit-content;
  min-width: 80px;
  height: 20px;
  border-radius: 20px;
  color: $white;
  font-weight: 700;
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  background-color: $purple;
  padding: 1px 9px;
  align-self: center;
  margin-bottom: 20px;
}
.element__block--my {
  width: 600px;
}
.element__block {
  align-self: flex-end;
}
.element__user-wrapper--my {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 5px;
}
.element__user-wrapper {
  flex-direction: row-reverse;
}
.element__user-wrapper--sameUser {
  justify-content: flex-end;
}
.element__time {
  display: flex;
  align-items: center;
  gap: 5px;
  color: $grey;
  font-size: 12px;
  line-height: 18px;
}
.user-data--my {
  display: flex;
  gap: 14px;
}
.user-data {
  flex-direction: row-reverse;
}
.avatar-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;

}
.fullname {
  color: $blue;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 1%;
}
.avatar {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  overflow: hidden;
}
.element__message--my {
  font-size: 14px;
  line-height: 24px;
  padding: 16px 20px 20px 20px;
  border-radius: 10px;
  background-color: $light-grey-2;
}
.element__message {
  background-color: $white;
  border: solid 1px $grey-disabled;
}
.edit {
  display: flex;
  gap: 7px;
  align-items: center;
  margin-right: 5px;
}
.edit svg {
  height: 18px;
  width: 18px;
  cursor: pointer;
  transition: all 0.3s;
}
.edit svg:hover {
  scale: 1.1;
}
