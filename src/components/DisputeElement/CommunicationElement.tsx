import React, { FC, useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import cx from 'classnames'

import styles from './CommunicationElement.module.scss'
import { TState } from 'store'
import { FormatDate } from 'components/UiKit/FormatDate'
import { ReactComponent as DefaultAvatar } from 'assets/images/default_avatar.svg'
import dayjs from 'utils/dayjs'
import { setEditMode } from 'screens/client/Subcontract/components/Communication/Communication.actions'
import { ITaskComments } from 'screens/client/Subcontract/components/Communication/Communication.reducer'
import { Icons } from 'components/Icons'
import { deleteTaskCommentThunx } from 'screens/client/Subcontract/components/Communication/Communication.thunk'

interface ICommunicationElementProps {
  elem: ITaskComments
  prevElem: ITaskComments | null
  scrollToInput: () => void
}

const CommunicationElement: FC<ICommunicationElementProps> = ({
  elem,
  prevElem,
  scrollToInput
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const currentUser = useSelector((state: TState) => state.auth.data)
  const { userPhoto, isMine, isTaskCancellation } = useMemo(() => {
    let isMy = false
    let isTaskCancel = false

    if (elem.authorId === currentUser.id) {
      isMy = true
    }
    if (elem.comment.includes('<-Comment on task cancellation->')) {
      isTaskCancel = true
    }

    return { userPhoto: elem.author.photo, isMine: isMy, isTaskCancellation: isTaskCancel }
  }, [elem.author])

  const dispatch = useDispatch()

  useEffect(() => {
    return () => {
      dispatch(setEditMode({ id: null, message: '', shouldScrollToBottom: true }))
    }
  }, [])

  const { isSameDate, isSameUser } = useMemo(() => {
    let sameDate = false
    let sameUser = false
    const date = dayjs(elem.createdAt).format('DD/MM/YYYY')
    const prevDate = prevElem && dayjs(prevElem.createdAt).format('DD/MM/YYYY')

    if (prevDate) {
      if (prevDate === date) {
        sameDate = true
      }
      if (elem.author.fullName === prevElem.author.fullName) {
        sameUser = true
      }
    }

    return { isSameDate: sameDate, isSameUser: sameUser }
  }, [elem, prevElem])

  const onEdit = () => {
    dispatch(setEditMode({ id: elem.id, message: elem.comment, shouldScrollToBottom: false }))
    scrollToInput()
  }

  const onDelete = () => {
    dispatch(deleteTaskCommentThunx(elem.id, elem.taskId))
  }

  return (
    <div className={styles.element__container}>
      {/* Date */}
      {!isSameDate && (
        <div className={styles.element__date}>
          <FormatDate
            dateSrc={elem.createdAt}
            format="DD.MM.YYYY"
            text={words['TODAY'] || 'Today'}
          />
        </div>
      )}

      {/* Block */}
      <div
        className={cx({
          [styles['element__block--my']]: true,
          [styles.element__block]: !isMine
        })}
      >
        <div
          className={cx({
            [styles['element__user-wrapper--my']]: true,
            [styles['element__user-wrapper']]: !isMine,
            [styles['element__user-wrapper--sameUser']]: isSameUser
          })}
        >
          {!isSameUser && (
            <div
              className={cx({
                [styles['user-data--my']]: true,
                [styles['user-data']]: !isMine
              })}
            >
              <div className={styles['avatar-block']}>
                {userPhoto ? (
                  <img className={styles.avatar} src={userPhoto} alt="avatar" />
                ) : (
                  <DefaultAvatar className={styles.avatar} />
                )}
              </div>
              <div className={styles['user-info']}>
                <h2 className={styles['fullname']}>{elem.author.fullName}</h2>
              </div>
            </div>
          )}
          <div className={styles.element__time}>
            {isMine && !isTaskCancellation && (
              <div className={styles.edit}>
                <Icons icon="edit" onClick={onEdit} />
                <Icons icon="delete" onClick={onDelete} />
              </div>
            )}
            <FormatDate dateSrc={elem.createdAt} format="HH:mm" />
          </div>
        </div>

        {/* Msg */}
        <div
          className={cx({
            [styles['element__message--my']]: true,
            [styles['element__message']]: !isMine
          })}
        >
          {elem.comment}
        </div>
      </div>
    </div>
  )
}

export default CommunicationElement
