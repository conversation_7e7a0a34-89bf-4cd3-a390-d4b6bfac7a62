import React from 'react'
// import { InjectedConnector } from '@web3-react/injected-connector'
// import { useWeb3React } from '@web3-react/core'
// import { Web3Provider } from '@ethersproject/providers'
// import useSWR from 'swr'
import styles from './ConnectWallet.module.scss'
// import { useSelector } from 'react-redux'
// import { TState } from 'store'

// const fetcher = (library: any) => (...args: any[]) => {
//   const [method, ...params] = args
//   return library[method](...params)
// }

export const ConnectWallet_TO_DELETE = () => {
  // const words = useSelector((state: TState) => state.global.language.words)
  // const injectedConnector = new InjectedConnector({ supportedChainIds: [137, 80001] })
  // const { account, activate, active, library } = useWeb3React<Web3Provider>()
  // const { data: balance } = useSWR(['getBalance', account, 'latest'], { fetcher: fetcher(library) })

  // const onClick = () => {
  //   activate(injectedConnector)
  // }

  return (
    <div className={styles.container}>
      {/* {active ? (
        <div>✅ {balance / 1e18} Matic</div>
      ) : (
        <button type="button" onClick={onClick}>
          {words['component.connectWallet.connect']}
        </button>
      )} */}
    </div>
  )
}
