import { RouteComponentProps } from 'react-router-dom'
import { mapDispatchToProps, mapStateToProps } from './CurrentTeam.container'

export interface IInitialState {
  members: any[]
  selectedMembers: []
  error: string
  loading: boolean
}

export type TProjectsProps = ReturnType<typeof mapStateToProps> &
  ReturnType<typeof mapDispatchToProps> &
  RouteComponentProps & {
    displayAllFields: boolean
  }
export interface IProjectPreview extends RouteComponentProps {
  avatar: string
  name: string
  id: string
}
