import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import cx from 'classnames'

import styles from './MessageInput.module.scss'
import { TState } from 'store'
import {
  setEditMode,
  setMessage
} from 'screens/client/Subcontract/components/Communication/Communication.actions'
import {
  createTaskCommentThunx,
  updateTaskCommentThunx
} from 'screens/client/Subcontract/components/Communication/Communication.thunk'
import { Icons } from 'components/Icons'

interface MessageInputProps {
  taskId: number
}

const MessageInput = ({ taskId }: MessageInputProps) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const enter_message = words['user.subcontract.enterMessage'] || 'Enter message'

  const comment = useSelector((state: TState) => state.client.communication.message)
  const commentId = useSelector((state: TState) => state.client.communication.messageId)

  const dispatch = useDispatch()

  const _onSubmit = (): void => {
    const trimComment = comment ? comment.trim() : comment

    if (!trimComment || !taskId) return
    const message = {
      taskId,
      comment: trimComment
    }

    if (commentId) {
      dispatch(updateTaskCommentThunx(commentId, message))
      return
    }

    dispatch(createTaskCommentThunx(message))
  }

  const _onCancel = () => {
    dispatch(setEditMode({ id: null, message: '', shouldScrollToBottom: false }))
  }

  // Onsubmit
  document.onkeyup = (event: any): void => {
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      _onSubmit()
    }
  }

  // Handler for text input
  const handleChange = (e: any): void => {
    dispatch(setMessage(e.target.value))
  }

  return (
    <form className={styles.msgInput__form}>
      <textarea
        id="Send message"
        name="SendMessage"
        value={comment}
        placeholder={`${enter_message}...`}
        onChange={handleChange}
        className={styles.msgInput__textarea}
      />
      <div
        className={cx({
          [styles.msgInput__sendIcon]: true,
          [styles['msgInput__sendIcon--disabled']]: !comment
        })}
      >
        {commentId ? (
          <div className={styles['msgInput__icons']}>
            <Icons icon="close" onClick={_onCancel} />
            <Icons icon="checkedMarkWhite" onClick={_onSubmit} />
          </div>
        ) : (
          <Icons icon="send" onClick={_onSubmit} />
        )}
      </div>
    </form>
  )
}

export default MessageInput
