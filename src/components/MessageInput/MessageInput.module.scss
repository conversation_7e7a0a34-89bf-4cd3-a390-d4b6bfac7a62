@import '../../assets/style/colors';

.msgInput__form {
  display: flex;
  padding: 10px 16px;
  border: 1px solid $blue;
  border-radius: 10px;
  margin-top: 20px;
}
.msgInput__textarea {
  width: 100%;
  height: 80px;
  resize: none;
  outline: none;
  border: none;
  font-size: 14px;
  line-height: 24px;
  color: $dark-grey;
}
.msgInput__sendIcon {
  display: flex;
  align-items: center;
  padding-left: 10px;
}
.msgInput__sendIcon svg {
  cursor: pointer;
}
.msgInput__sendIcon svg path {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.msgInput__sendIcon svg {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.msgInput__sendIcon svg:hover {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
}
.msgInput__sendIcon--disabled svg path {
  opacity: 0.5;
}
.msgInput__sendIcon--disabled:hover svg {
  cursor: not-allowed;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.msgInput__icons {
  display: flex;
  align-items: center;
  gap: 20px;
}
.msgInput__icons svg {
  width: 30px;
  height: 30px;
}
