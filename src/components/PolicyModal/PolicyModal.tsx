import React, { FC, useEffect, useRef, useState } from 'react'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Bold from '@tiptap/extension-bold'
import Italic from '@tiptap/extension-italic'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import { Tooltip as ReactTooltip } from 'react-tooltip'

import styles from './style.module.scss'
import { BaseButton } from 'components/UiKit/Button/BaseButton'
import { useAuth } from 'hooks/auth.hook'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import Modal from 'components/Modal'

type TModalProps = {
  acceptPolicy: any
  content: string
  isPreviewMode: boolean
}

const PolicyModal: FC<TModalProps> = ({ acceptPolicy, content, isPreviewMode }) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const close_w = words['component.notifications.close']
  const accept_w = words['admin.policy.accept']
  const scrollDown_w = words['admin.policy.scrollDown']
  const decline_w = words['admin.policy.decline']

  const { logoutWithRedirect } = useAuth()
  const [isScrolledToEnd, setIsScrolledToEnd] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  const editor = useEditor({
    extensions: [
      StarterKit,
      Bold,
      Italic,
      Underline,
      TextStyle,
      TextAlign.configure({
        types: ['heading', 'paragraph']
      }),
      Link.configure({ openOnClick: true, protocols: ['https'] }),
      Table.configure({
        resizable: true
      }),
      TableRow,
      TableCell,
      TableHeader
    ],
    content: '',
    editable: false
  })

  useEffect(() => {
    if (editor && content) {
      editor.commands.setContent(content, false)
      const contentBlock = contentRef.current
      if (contentBlock) {
        contentBlock.scrollTop = 0
        const hasScrollbar = contentBlock.scrollHeight > contentBlock.clientHeight
        if (hasScrollbar) {
          setIsScrolledToEnd(false)
        } else {
          setIsScrolledToEnd(true)
        }
      }
    }
  }, [content, editor])

  useEffect(() => {
    if (isPreviewMode) {
      setIsScrolledToEnd(true)
      return
    }

    const contentBlock = contentRef.current
    if (!contentBlock) return

    // Force scroll down 1px to trigger scrollHeight update contentBlock.scrollTop = 1
    contentBlock.scrollTop = 1

    setTimeout(() => {
      requestAnimationFrame(() => {
        if (!contentBlock) return

        const hasScrollbar = contentBlock.scrollHeight > contentBlock.clientHeight

        if (!hasScrollbar) {
          setIsScrolledToEnd(true)
        } else {
          contentBlock.scrollTop = 1
        }
      })
    }, 500)

    const handleScroll = () => {
      if (contentBlock.scrollTop + contentBlock.clientHeight >= contentBlock.scrollHeight - 1) {
        setIsScrolledToEnd(true)
      }
    }

    contentBlock.addEventListener('scroll', handleScroll)

    return () => {
      contentBlock.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const closeModal = () => {
    if (isPreviewMode) {
      acceptPolicy()
      return
    }
    localStorage.clear()
    logoutWithRedirect()
  }

  return (
    <Modal isShow={true} onClose={closeModal}>
      <div className={styles.container}>
        <div className={styles['editor-content']} ref={contentRef}>
          <EditorContent editor={editor} />
        </div>

        <div className={styles['button-container']}>
          <BaseButton children={decline_w} size={'lgs'} onClick={closeModal} />
          <BaseButton
            children={isPreviewMode ? close_w : accept_w}
            outline={true}
            size={'lgs'}
            disabled={!isScrolledToEnd}
            onClick={() => {
              acceptPolicy()
            }}
            id="acceptPolicy"
          />
        </div>

        {!isScrolledToEnd && (
          <ReactTooltip
            className={styles.tooltip}
            place="top"
            anchorSelect="#acceptPolicy"
            content={scrollDown_w}
          />
        )}
      </div>
    </Modal>
  )
}

export default PolicyModal
