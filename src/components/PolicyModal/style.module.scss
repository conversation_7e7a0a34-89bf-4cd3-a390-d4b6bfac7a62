@import '../../assets/style/colors';
@import '../../assets/style/variables';
@import '../../assets/style/mixins';

.container {
  overflow: hidden;

  display: flex;
  justify-content: space-between;
  flex-direction: column;
  color: #333;
  text-align: start;
  height: 100%;

  .button-container {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
  }
}

.editor-content {
  overflow-y: auto;
  background-color: $white;
  width: 100%;
  height: 100%;
  min-width: 900px;
  height: 380px;
  border: 1px solid #EBEBEB;
  border-radius: 14px;
  padding: 20px 40px;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05);
}

.editor-content table,
.editor-content th,
.editor-content td {
  border: 1px solid $black;
  border-collapse: collapse;
}

.editor-content td,
.editor-content th {
  position: relative;
  padding: 5px;
  text-align: left;
}

.editor-content table {
  width: 100%;
  table-layout: fixed;
  margin: 0;
  overflow: hidden;

  p {
    margin: 0;
  }
}

.tooltip {
  border-radius: 10px;
  padding: 5px 10px;
  min-height: 25px;
  font-family: 'Open Sans', sans-serif;
  text-align: center;
  font-size: 12px;
  line-height: 12px;
  background-color: $white;
  color: $dark-grey;
  box-shadow: $tooltip-shadow;
  z-index: 10000;
}
