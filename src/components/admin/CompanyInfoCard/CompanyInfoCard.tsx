import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { AbstractSimpleInfoCard } from '../AbstractSimpleInfoCard/AbstractSimpleInfoCard'
import { ICompanyData } from 'models/admin-settings-models'

interface ICompanyInfoCardProps {
  company: ICompanyData
  onSelect: () => void
  onDelete: () => void
}

export const CompanyInfoCard: React.FC<ICompanyInfoCardProps> = ({
  company,
  onSelect,
  onDelete
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const translatedNameField = {
    label: words['admin.settings.companyData.translatedName'] || 'Translated Name',
    value: company.translatedName
  }
  const translatedAddressField = {
    label: words['admin.settings.companyData.translatedAddress'] || 'Translated Address',
    value: company.translatedAddress
  }

  const array = [
    { label: words['admin.settings.companyData.name'] || 'Name', value: company.name },
    ...(company.translatedName ? [translatedNameField] : []),
    {
      label: words['admin.settings.companyData.businessNumber'] || 'Business Number',
      value: company.businessNumber
    },
    {
      label: words['admin.settings.companyData.incorporationNumber'] || 'Incorporation Number',
      value: company.incorporationNumber
    },
    { label: words['admin.settings.companyData.address'] || 'Address', value: company.address },
    ...(company.translatedAddress ? [translatedAddressField] : [])
  ]

  return <AbstractSimpleInfoCard array={array} onEdit={onSelect} onDelete={onDelete} />
}
