@import 'assets/style/colors';

.card {
  display: flex;
  column-gap: 32px;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background-color: $white;
  border: 1px solid $light-grey;
  box-shadow: 0px 0px 20px rgba(51, 51, 51, 0.05);
  border-radius: 14px;
}

.info-container {
  display: flex;
  flex-wrap: wrap;
  column-gap: 32px;
  row-gap: 12px;
}

.info-block {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 240px;
  text-align: left;

  .subtitle {
    color: $blue;
    font-size: 14px;
    font-weight: 700;
  }
}

.btn-block {
  display: flex;
  justify-content: flex-end;
  gap: 32px;

  .button {
    padding: 4px 12px;
    height: 32px;
    cursor: pointer;
  }
}
