import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { Button } from 'components/UiKit/Button'
import { SkeletonMui } from 'components/Skeleton'

import styles from './AbstractSimpleInfoCard.module.scss'

interface IDataElem {
  label: string
  value?: string | null
}

interface IAbstractSimpleInfoCardProps {
  array: IDataElem[]
  onEdit: () => void
  onDelete: () => void
}

export const AbstractSimpleInfoCard: React.FC<IAbstractSimpleInfoCardProps> = ({
  array,
  onEdit,
  onDelete
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <div className={styles['card']}>
      <div className={styles['info-container']}>
        {array.map(data => (
          <div key={data.label} className={styles['info-block']}>
            <span className={styles['subtitle']}>{data.label}</span>
            {data.value || <SkeletonMui />}
          </div>
        ))}
      </div>
      <div className={styles['btn-block']}>
        <Button className={styles['button']} onClick={onEdit}>
          {words['abstract.button.edit'] || 'Edit'}
        </Button>
        <Button className={styles['button']} onClick={onDelete}>
          {words['abstract.button.delete'] || 'Delete'}
        </Button>
      </div>
    </div>
  )
}
