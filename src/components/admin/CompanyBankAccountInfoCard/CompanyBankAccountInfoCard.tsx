import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { AbstractSimpleInfoCard } from '../AbstractSimpleInfoCard/AbstractSimpleInfoCard'
import { IBankAccountWithDetails } from 'models/admin-settings-models'

interface ICompanyBankAccountInfoCardProps {
  bankAccount: IBankAccountWithDetails
  onSelect: () => void
  onDelete: () => void
}

export const CompanyBankAccountInfoCard: React.FC<ICompanyBankAccountInfoCardProps> = ({
  bankAccount,
  onSelect,
  onDelete
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const translatedTypeField = {
    label: words['admin.settings.companyBankAccounts.translatedType'] || 'Translated Type',
    value: bankAccount.translatedType
  }

  const array = [
    {
      label: words['admin.settings.companyBankAccounts.bank'] || 'Bank',
      value: bankAccount.bank.name
    },
    {
      label: words['admin.settings.companyBankAccounts.currency'] || 'Currency',
      value: bankAccount.currency.name
    },
    { label: words['admin.settings.companyBankAccounts.iban'] || 'Iban', value: bankAccount.iban },
    { label: words['admin.settings.companyBankAccounts.type'] || 'Type', value: bankAccount.type },
    ...(bankAccount.translatedType ? [translatedTypeField] : []),
    {
      label: words['admin.settings.companyBankAccounts.routingNumber'] || 'Routing number',
      value: bankAccount.routingNumber
    }
  ]

  return <AbstractSimpleInfoCard array={array} onEdit={onSelect} onDelete={onDelete} />
}
