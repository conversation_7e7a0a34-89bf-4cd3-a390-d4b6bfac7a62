import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { AbstractSimpleInfoCard } from '../AbstractSimpleInfoCard/AbstractSimpleInfoCard'
import { ICurrency } from 'models/admin-settings-models'

interface ICurrencyInfoCardProps {
  currency: ICurrency
  onSelect: () => void
  onDelete: () => void
}

export const CurrencyInfoCard: React.FC<ICurrencyInfoCardProps> = ({
  currency,
  onSelect,
  onDelete
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const array = [
    { label: words['admin.settings.currencies.name'] || 'Name', value: currency.name },
    {
      label: words['admin.settings.currencies.translatedName'] || 'Translated Name',
      value: currency.translatedName
    }
  ]

  return <AbstractSimpleInfoCard array={array} onEdit={onSelect} onDelete={onDelete} />
}
