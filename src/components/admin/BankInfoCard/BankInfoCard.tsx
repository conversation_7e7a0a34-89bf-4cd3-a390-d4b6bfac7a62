import React from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { AbstractSimpleInfoCard } from '../AbstractSimpleInfoCard/AbstractSimpleInfoCard'
import { IBank } from 'models/admin-settings-models'

interface IBankInfoCardProps {
  bank: IBank
  onSelect: () => void
  onDelete: () => void
}

export const BankInfoCard: React.FC<IBankInfoCardProps> = ({ bank, onSelect, onDelete }) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const translatedNameField = {
    label: words['admin.settings.banks.translatedName'] || 'Translated Name',
    value: bank.translatedName
  }
  const translatedAddressField = {
    label: words['admin.settings.banks.translatedAddress'] || 'Translated Address',
    value: bank.translatedAddress
  }

  const array = [
    { label: words['admin.settings.banks.name'] || 'Name', value: bank.name },
    ...(bank.translatedName ? [translatedNameField] : []),
    { label: words['admin.settings.banks.address'] || 'Address', value: bank.address },
    ...(bank.translatedAddress ? [translatedAddressField] : []),
    { label: words['admin.settings.banks.swift'] || 'SWIFT', value: bank.swift }
  ]

  return <AbstractSimpleInfoCard array={array} onEdit={onSelect} onDelete={onDelete} />
}
