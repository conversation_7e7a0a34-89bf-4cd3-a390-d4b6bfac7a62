import React, { <PERSON> } from 'react'
import { withRouter, NavLink } from 'react-router-dom'
import { IAccordionItem } from './model'
import cls from 'classnames'
import styles from './style.module.scss'

const AccordionItem: FC<IAccordionItem> = ({ to, title, isActive, onclickItem }) => (
  <li className={styles.accordion_li}>
    <NavLink
      exact={true}
      className={cls(styles.accordion_a, { [styles.accordion_a_active]: isActive })}
      to={to}
      onClick={(e: any) => {
        e.preventDefault()
        onclickItem(to)
      }}
    >
      {title}
    </NavLink>
  </li>
)

export default withRouter(AccordionItem)
