import React, { FC, forwardRef, useMemo } from 'react'
import cls from 'classnames'
import dayjs from 'utils/dayjs'
import { ITaskInvoice, ETaskType, ETaskInvoiceStatus } from 'models'
import { ETaskStatus } from 'screens/client/Subcontract/components/CreateTask/CreateTask.model'

import logo from '../../assets/images/logo-black.svg'

import styles from './InvoiceTask.module.scss'

interface IDataItem {
  text: string
  value: number | string
  isPrimary?: boolean
}
interface IDataColumn {
  head: string
  value: number | string
  width?: number
}

interface IInvoiceTaskProps {
  ref?: any
  invoice: ITaskInvoice
}

const InvoiceTask: FC<IInvoiceTaskProps> = forwardRef(({ invoice }, ref: any) => {
  const token = invoice.task.token ? invoice.task.token.symbol : 'UNKNOWN'
  const withToken = (value: number | string) => `${value} ${token}`
  const parseDate = (date: string) => dayjs(date).format('DD.MM.YYYY')

  const taskInvoiceStatus = useMemo(() => {
    switch (invoice.task.status.id) {
      case ETaskStatus.COMPLETED:
        return ETaskInvoiceStatus.COMPLETED
      case ETaskStatus.CANCELLED:
      case ETaskStatus.CANCELLED_BY_VOTING:
      case ETaskStatus.CANCELLED_BY_SUPPORT_COMMENT_FROM_CUSTOMER:
      case ETaskStatus.CANCELLED_BY_SUPPORT_COMMENT_FROM_CONTRACTOR:
      case ETaskStatus.CANCELLED_BY_SUPPORT_COMMENT_FROM_BOTH:
        return ETaskInvoiceStatus.CANCELED
      default:
        return ETaskInvoiceStatus.UNKNOWN
    }
  }, [invoice.task.status])

  const type = {
    [ETaskType.DEDICATED_TEAM]: 'Dedicated Team',
    [ETaskType.TIME_AND_MATERIALS]: 'Time And Materials',
    [ETaskType.FIXED_PRICE]: 'Fixed Price',
    [ETaskType.PROJECT_BASED]: 'Project Based'
  }[invoice.task.type as ETaskType]

  const receiver =
    invoice.task.type === ETaskType.PROJECT_BASED
      ? invoice.task.companyName
      : invoice.task.executor.fullName

  const personList = useMemo(
    () => [
      { text: 'Client', value: invoice.task.customer.fullName },
      { text: 'Contractor', value: receiver },
      { text: 'Issued by', value: 'Points System (Technorely)' }
    ],
    [invoice.task, receiver]
  )

  const dataItems = useMemo(() => {
    const array: IDataItem[] = [
      { text: 'Model of working', value: type },
      { text: 'Date of Payment', value: dayjs(invoice.dateOfPayment).format('MMM Do YYYY') },
      { text: 'Invoice total', value: withToken(invoice.negotiatedAmount.toFixed(2)) },
      {
        text: `Platform Service Fee (${invoice.percentageFee}%)`,
        value: withToken(invoice.computedFee)
      }
    ]

    if (taskInvoiceStatus === ETaskInvoiceStatus.COMPLETED) {
      array.push({
        text: 'Net Amount to Contractor',
        value: withToken(invoice.refundContractorAmount),
        isPrimary: true
      })
    }

    if (taskInvoiceStatus === ETaskInvoiceStatus.CANCELED) {
      array.push({ text: 'Distributable Amount', value: withToken(invoice.pureAmount) })
      array.push({
        text: `Refund to Client (${invoice.refundCustomerPercent.toFixed(2)}%)`,
        value: withToken(invoice.refundCustomerAmount)
      })
      array.push({
        text: `Contractor Compensation (${invoice.refundContractorPercent.toFixed(2)}%)`,
        value: withToken(invoice.refundContractorAmount),
        isPrimary: true
      })
    }

    return array
  }, [])

  const columns = useMemo(() => {
    if (!invoice || !invoice.task || !Object.values(ETaskType).includes(invoice.task.type))
      return []

    const array: IDataColumn[] = []
    if ([ETaskType.DEDICATED_TEAM, ETaskType.PROJECT_BASED].includes(invoice.task.type)) {
      array.push({
        head: 'Specialization',
        value: (invoice.task.specializations || []).map(({ label }) => label).join('\n')
      })
    }
    if (![ETaskType.FIXED_PRICE, ETaskType.TIME_AND_MATERIALS].includes(invoice.task.type)) {
      array.push({
        head: 'Period',
        value: invoice.task.startProjectDate
          ? parseDate(invoice.task.startProjectDate) + ' - ' + parseDate(invoice.task.deadline)
          : 'no terms'
      })
    }
    if (invoice.task.type === ETaskType.TIME_AND_MATERIALS) {
      array.push({ head: 'Hours', value: invoice.task.workingHours || '---', width: 10 })
      array.push({
        head: 'Rate',
        value: invoice.task.pricePerHour ? `${invoice.task.pricePerHour} ${token}` : '---',
        width: 10
      })
    }
    if (invoice.task.type === ETaskType.FIXED_PRICE) {
      array.push({
        head: 'Date of Payment',
        value: parseDate(invoice.dateOfPayment || new Date().toString())
      })
    }

    array.push({ head: 'Total Price', value: withToken(invoice.negotiatedAmount) })
    array.push({ head: 'Platform Service Fee', value: withToken(invoice.computedFee) })

    if (taskInvoiceStatus === ETaskInvoiceStatus.CANCELED) {
      array.push({ head: 'Refund to Client', value: withToken(invoice.refundCustomerAmount) })
    }

    array.push({
      head: 'Contractor Compensation',
      value: withToken(invoice.refundContractorAmount)
    })

    return array
  }, [])

  return (
    <div ref={ref} className={styles['invoice']}>
      <div className={styles['main-data']}>
        <div className={styles['row-block']}>
          <div className={styles['left-side']}>
            <div className={styles['main-head']}>
              <h3 className={styles['title']}>Invoice</h3>
              <span className={styles['number']}>№ {String(invoice.id).padStart(6, '0')}</span>
            </div>
            <ul className={styles['person-list']}>
              {personList.map(({ text, value }, idx) => (
                <li key={idx} className={styles['person']}>
                  <span className={styles['subtitle']}>{text}:</span>
                  <span className={styles['value']}>{value}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className={styles['right-side']}>
            <img className={styles['logo']} src={logo} />
            <ul className={styles['list']}>
              {dataItems.map(({ text, value, isPrimary }, idx) => (
                <li key={idx} className={cls([styles['item'], { [styles['primary']]: isPrimary }])}>
                  <span className={styles['text']}>{text}:</span>
                  <span className={styles['value']}>{value}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className={styles['task-block']}>
          <p className={styles['subtitle']}>Task Title:</p>
          <p className={styles['value']}>{invoice.task.title}</p>
        </div>
      </div>
      <table className={styles['table']}>
        <thead>
          <tr>
            {columns.map(({ head, width }, idx) => (
              <th key={idx} style={{ width: `${width}%` }}>
                {head}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          <tr>
            {columns.map(({ value }, idx) => (
              <td key={idx}>{value}</td>
            ))}
          </tr>
        </tbody>
      </table>
      <div className={styles['notes-block']}>
        <p className={styles['subtitle']}>Notes:</p>
        {taskInvoiceStatus === ETaskInvoiceStatus.CANCELED && (
          <>
            <p>Deal not completed.</p>
            <p>
              As per the agreement, although the deal was not completed, the contractor is entitled
              to a {invoice.refundContractorPercent}% compensation. The platform commission is also
              non-refundable. The remaining amount is refunded to the client.
            </p>
          </>
        )}
        <p>This invoice is valid without a signature and stamp. Issued electronically.</p>
      </div>
    </div>
  )
})

export default InvoiceTask
