@import 'assets/style/colors';

.invoice {
  width: 720px;
  font-size: 10px;
  line-height: 16px;

  h1, h2, h3, h4, h5, h6, p {
    margin: 0;
  }
}

.subtitle {
  color: $grey;
  font-size: 10px;
  line-height: 16px;
  font-weight: 700;
  text-transform: uppercase;
}

.main-data {
  padding: 30px 30px 0;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .row-block {
    display: flex;
    justify-content: space-between;
    gap: 32px;
  }

  .left-side,
  .right-side {
    display: flex;
    flex-direction: column;
  }

  .left-side {
    text-align: left;
    max-width: 200px;
  }

  .right-side {
    width: 300px;
  }
}

.main-head {
  display: flex;
  flex-direction: column;
  
  .title {
    color: $dark-grey;
    font-size: 32px;
    line-height: 100%;
    letter-spacing: 0.01em;
  }
  
  .number {
    color: $grey;
    font-size: 12px;
    line-height: 100%;
  }
}

.person-list {
  margin-top: 56px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.person {
  display: flex;
  flex-direction: column;

  .value {
    font-weight: 700;
    text-transform: uppercase;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

.logo {
  width: 200px;
  margin-left: auto;
}

.list {
  margin: 86px 0 0;
  display: flex;
  flex-direction: column;
}

.item {
  padding: 10px;
  display: flex;
  justify-content: space-between;

  .text {
    color: $grey;
    text-align: right;
  }

  .value {
    color: $dark-grey;
    font-weight: 700;
  }

  &.primary {
    font-weight: 700;
    background: #F9F9FF;

    .text,
    .value {
      color: $green;
    }
  }
}

.task-block {
  display: flex;
  gap: 24px;

  .subtitle {
    white-space: nowrap;
  }

  .value {
    font-weight: 700;
  }
}

.table {
  border-spacing: 0;
  width: 100%;
  margin-top: 32px;
  font-size: 10px;
  border-collapse: collapse;

td {
    padding: 12px;
    text-align: center;
    border-right: 1px solid #BBBBFF;
  }

  th {
    padding: 12px;
    text-align: center;
    color: $dark-grey;
    background: #F9F9FF;
    border-right: 1px solid #BBBBFF;
  }

  td:last-child, th:last-child {
    border: none;
  }

  tr {
    border-bottom: 1px solid #BBBBFF;
  }
}

.notes-block {
  margin-top: 20px;
  padding: 0 30px;
}
