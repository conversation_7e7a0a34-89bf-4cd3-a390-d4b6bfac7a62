import React, { <PERSON> } from 'react'
import { useSelector } from 'react-redux'
import { IToken } from 'screens/admin/Token/Token.model'
import { TState } from 'store'
import styles from './TokenField.module.scss'
import { Icons } from 'components/Icons'
import cls from 'classnames'

interface ITokenField {
  token: IToken
  openModal: () => void
  disabled: boolean
  error: boolean
}

export const TokenField: FC<ITokenField> = ({ token, openModal, disabled, error }) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <div
      onClick={() => {
        disabled ? null : openModal()
      }}
    >
      <div className={styles.fieldDescription}>
        {words['user.subcontract.task.currency']}
        <span
          className={cls({
            [styles['red-note']]: true,
            [styles['disabled']]: disabled
          })}
        >
          *
        </span>
      </div>

      <div
        className={cls({
          [styles.field]: true,
          [styles['opacity']]: disabled,
          [styles['error']]: error
        })}
      >
        {token && (
          <>
            <img src={`${token.logoURI}`} className={styles.tokenImg} />
            <span className={styles.tokenName}>{token.symbol}</span>
            <Icons icon={'arrowDown'} style={{ marginLeft: 'auto' }} />
          </>
        )}
      </div>
    </div>
  )
}
