import { IToken } from 'screens/admin/Token/Token.model'
import { mapDispatchToProps, mapStateToProps } from './TaskInfo.container'
import { ITaskInfo } from 'screens/client/Subcontract/Subcontract.model'

export type TTaskInfoProps = ReturnType<typeof mapDispatchToProps> &
  ReturnType<typeof mapStateToProps> & {
    deadline?: any
    task: ITaskInfo
    setRerender?: any
    rerender?: any
    getTasks?: any
    tokens?: IToken[]
    extraStatus?: string
  }

export type TTaskInfo = {
  type: string
  payload?: any
}
