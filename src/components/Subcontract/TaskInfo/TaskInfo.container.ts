import { connect } from 'react-redux'
import { bindActionC<PERSON>s, Dispatch } from 'redux'
import { TState } from 'store'

import TaskInfoComponent from './TaskInfo'
import { acceptTaskThunk, agreeProposalThunk, signTaskThunk } from 'screens/client/Web3/web3.thunk'
import { updateTaskInfo } from '../../../screens/client/Subcontract/components/CreateTask/CreateTask.thunk'
import { rejectOfferByCandidatThunk } from 'screens/client/Subcontract/Subcontract.thunk'

export const mapStateToProps = (state: TState) => {
  return {
    loading: state.client.createTask.loading,
    accessToken: state.auth.token,
    user: state.auth.data
  }
}
export const mapDispatchToProps = (dispatch: Dispatch) =>
  bindActionCreators(
    {
      signTaskThunk,
      acceptTaskThunk,
      updateTaskInfo,
      agreeProposalThunk,
      rejectOfferByCandidatThunk
    },
    dispatch
  )

export const TaskInfo = connect(
  mapStateToProps,
  mapDispatchToProps
)(TaskInfoComponent)
