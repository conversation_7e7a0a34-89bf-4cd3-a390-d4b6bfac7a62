@import '../../../assets/style/colors';
@import '../../../assets/style/mixins';

.info-section,
.filesSection {
  @include profile-info-section;
  cursor: pointer;
  padding: 21px 24px 14px;
  min-height: 160px;

  h2 {
    text-align: left;
  }

  .info-container {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    text-align: left;
    justify-content: space-between;
    flex-direction: column;

    p {
      font-size: 14px;
      line-height: 24px;
      margin: 0px 0px 8px;
      max-width: 73%;
    }

    .meta-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      text-wrap: nowrap;

        p {
          margin: 0px 50px 0px 0px;

          i {
            position: relative;
            top: 3px;
            margin-right: 8px;
          }
        }

      .about-price {
        display: flex;
        text-align: right;
        
        .price {
            opacity: 1 !important;
            color: #149B58;
        }

        .job_time {
          font-size: 14px;
          font-weight: 400;
          line-height: 24px;
          margin: 3px 0px 0px 0px;
        }

        .orange {
          color: $orange !important;
        }

        .custom {
          font-size: 10px;
          line-height: 14px;
          opacity: 0.6;
          color: $grey;
        }
      }
    }
  }

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.price {
  width: max-content;
  margin-left: 50px;
  opacity: 1 !important;
  color: #149B58;
}

.onetask {
  text-decoration: none;
  color: $dark-grey;
}

.canceled,
.work,
.interwiev,
.ready,
.created,
.completed {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  min-width: 90px;
  width: max-content;
  font-size: 13px;
  font-weight: 600;
  text-align: center;
  border-radius: 10px;
  padding-left: 10px;
  padding-right: 10px;
}

.created {
  color: $points_icon;
  background-color: $pink-light;
}
.completed {
  color: $blue;
  background-color: $light-blue;
}

.interwiev {
  color: $orange;
  background-color: $orange-light;
  min-width: 85px;
}

.work {
  color: $green;
  background-color: $light-green;
}

.canceled {
  color: $red;
  background-color: $light-red;
}

.btn {
  min-width: max-content;
  width: 210px;
  height: 32px;
  font-size: 14px;
  line-height: 24px;
  padding-top: 0px;
  padding-bottom: 0px;
  cursor: pointer;

  &-offer {
    width: 130px;
  }

  &-request {
    width: 180px;
    height: 32px;
  }
}

.wrapper {
  display: flex;
  max-width: max-content;
  align-items: center;
}

.hr {
  border: 0.5px solid $light-grey;
  width: 100%;
  margin: 14px 0;
}

.comment {
  margin: 0;
  font-size: 14px;
  line-height: 24px;
}

.offer-title {
  color: $blue;
  margin-bottom: 10px;
}

.offer-descr-container {
  display: flex;
  column-gap: 36px;
  align-items: center;
  margin-top: 16px;

  .descr-item {
    color: $green;
    margin: 0;
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;

    span {
      color: $dark-grey;
      margin-right: 5px;
    }
  }

  .offer-date {
    color: $grey;
    margin-left: auto;
  }
}

.btns-wrapper {
  width: fit-content;
  display: flex;
  align-items: center;
  
  button:first-of-type {
    margin-right: 20px;
  }
}

.task-number {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color: $grey;
}
