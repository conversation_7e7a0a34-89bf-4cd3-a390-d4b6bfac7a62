import React, { FC, MouseEvent, startTransition, useMemo } from 'react'
import { useHistory, useParams } from 'react-router'
import cls from 'classnames'
import { useSelector } from 'react-redux'

import { TState } from 'store'
import { Icons } from 'components/Icons'
import { BaseButton } from 'components/UiKit/Button/BaseButton'
import { ESubcontractHistory } from 'globalConfigs'
import { TTaskInfoProps } from './TaskInfo.model'
import styles from './TaskInfo.module.scss'
import dayjs from 'utils/dayjs'
import { useQueryParams } from 'hooks/useQueryParams'
import { ETaskType } from 'models'
import { ETaskResponseStatus } from 'screens/client/Subcontract/components/Response/libs/constants/constants'
import { EDealStatus } from 'screens/client/Web3/web3.model'
import { ITaskInfo } from 'screens/client/Subcontract/Subcontract.model'

const TaskInfo: FC<TTaskInfoProps> = ({
  task,
  signTaskThunk,
  acceptTaskThunk,
  extraStatus,
  user,
  agreeProposalThunk,
  rejectOfferByCandidatThunk
}) => {
  const history = useHistory()
  const words = useSelector((state: TState) => state.global.language.words)
  const currentLanguage = useSelector((state: TState) => state.global.language.currentLanguage)

  const parsed = useQueryParams()
  const myAnswers = parsed.task === ESubcontractHistory.MY_ANSWERS
  const isDraft = parsed.task === ESubcontractHistory.DRAFTS
  const id = task.id
  const dealStatus = task.dealStatus
  // const status = extraStatus || task.status.id
  const status = extraStatus || dealStatus
  const userId = user.id
  const isCustomer = task.customer.id === userId
  const isExecutor = task.executor && task.executor.id === userId

  const params: any = useParams()
  const isOfferMe = useMemo(() => {
    if (
      myAnswers &&
      task.taskResponses &&
      task.taskResponses.length &&
      !task.taskResponses[0].makeProposalSignature
    ) {
      return task.taskResponses[0]
    }
    return null
  }, [task])

  const handleAcceptTask = (event: MouseEvent) => {
    event.stopPropagation()
    acceptTaskThunk(task, 'taskList')
  }

  const handleSignTask = async (event: MouseEvent): Promise<void> => {
    event.stopPropagation()
    event.preventDefault()

    try {
      await signTaskThunk(task, history)
    } catch (err) {
      console.error('Sign task error:', err)
    }
  }

  const showPriceInfo = () => {
    if (!Object.keys(task).length || !task.token) return null

    const { pricePerHour, token, type, price } = task

    if (!token) return

    switch (type) {
      case ETaskType.TIME_AND_MATERIALS:
        return `${pricePerHour} ${token.symbol} / ${words['user.subcontract.perHour']}`

      case ETaskType.PROJECT_BASED:
        return `${price} ${token.symbol} / ${words['user.subcontract.perMonth']}`

      case ETaskType.DEDICATED_TEAM:
        return `${price} ${token.symbol}`

      default:
        return null
    }
  }

  const openCurrentTask = (extraParam = '') => {
    startTransition(() => {
      history.push(`/dashboard/subcontract/${id}?${extraParam}`)
    })
  }

  const openOfferPage = () => {
    if (params.candidatId) {
      localStorage.setItem('routes', JSON.stringify({ taskId: id, candidatId: params.candidatId }))
      startTransition(() => {
        history.push(`/dashboard/subcontract/offer`)
      })
    }
  }

  const agreeWithOffer = () => {
    agreeProposalThunk(task)
  }
  const rejectOffer = () => {
    if (task.taskResponses.length) {
      const body = {
        status: ETaskResponseStatus.REJECTED_BY_CANDIDATE,
        isNotification: true,
        title: 'user.subcontract.notification.offerUpdated',
        message: 'user.subcontract.notification.offerWasRejected',
        senderId: userId,
        receiverId: task.customer.id
      }
      const taskResponsId = task.taskResponses[0].id

      rejectOfferByCandidatThunk(id, taskResponsId, body)
    }
  }

  const renderTaskResponsStatusName = (taskR: ITaskInfo) => {
    // Status name
    return taskR.taskResponses && taskR.taskResponses[0] ? (
      <div className={styles.interwiev}>
        {words[`user.subcontract.taskResponse.status.${taskR.taskResponses[0].status}`] ||
          taskR.taskResponses[0].status}
      </div>
    ) : (
      <></>
    )
  }

  const renderStatusButton = () => {
    if (isExecutor || isCustomer) {
      switch (dealStatus) {
        case EDealStatus.RequestCancelByContractor:
          return <div className={styles.canceled}>{words['TASK_STATUS_11']}</div>
        case EDealStatus.RequestCancelByClient:
          return <div className={styles.canceled}>{words['TASK_STATUS_15']}</div>
        case EDealStatus.Canceled:
          return <div className={styles.canceled}>{words['TASK_STATUS_6']}</div>
        case EDealStatus.StopedWithProblem:
          return <div className={styles.canceled}>{words['TASK_STATUS_8']}</div>
        case EDealStatus.CanceledBySupportCommentFromBoth:
          return <div className={styles.canceled}>{words['TASK_STATUS_14']}</div>
        case EDealStatus.CanceledBySupportCommentFromContractor:
          return <div className={styles.canceled}>{words['TASK_STATUS_13']}</div>
        case EDealStatus.CanceledBySupportCommentFromCustomer:
          return <div className={styles.canceled}>{words['TASK_STATUS_12']}</div>
        case EDealStatus.CanceledByVoting:
          return (
            <div className={styles.canceled}>
              {words['TASK_STATUS_18'] || 'Cancelled by voting'}
            </div>
          )
      }
    }

    if (myAnswers) {
      // The task is being done by someone else, but I made a request for it.
      if (
        !isExecutor &&
        !isCustomer &&
        dealStatus !== EDealStatus.Created &&
        task.taskResponses.length
      ) {
        return renderTaskResponsStatusName(task)
      }

      if (
        (task.offerRequests && task.offerRequests.length) ||
        (task.interviewRequests && task.interviewRequests.length)
      ) {
        return (
          <BaseButton
            children={
              task.offerRequests.length
                ? words['user.subcontract.hire']
                : words['user.subcontract.confirmInterview']
            }
            size="percent"
            className={styles['btn-request']}
            onClick={(e: any) => {
              e.stopPropagation()
              openCurrentTask('response=calls')
            }}
          />
        )
      }

      if (dealStatus === EDealStatus.InProgress) {
        return <div className={styles.work}>{words['TASK_STATUS_5']}</div>
      }

      if (dealStatus === EDealStatus.Executed) {
        return <div className={styles.work}>{words['TASK_STATUS_9']}</div>
      }

      if (dealStatus === EDealStatus.Done) {
        return <div className={styles.completed}>{words['TASK_STATUS_3']}</div>
      }

      if (isOfferMe) {
        if (isOfferMe.status === ETaskResponseStatus.WAITING) {
          return (
            <div className={styles['btns-wrapper']}>
              <BaseButton
                children={words['user.subcontract.reject']}
                className={cls(styles['btn-request'], styles.btn)}
                outline
                onClick={(e: any) => {
                  e.stopPropagation()
                  rejectOffer()
                }}
              />
              <BaseButton
                children={words['user.subcontract.agree'] || 'Agree'}
                className={cls(styles['btn-request'], styles.btn)}
                onClick={(e: any) => {
                  e.stopPropagation()
                  agreeWithOffer()
                }}
              />
            </div>
          )
        }
        return (
          <div className={styles.canceled}>
            {words[`user.subcontract.taskResponse.status.${isOfferMe.status}`] || isOfferMe.status}
          </div>
        )
      }

      // Status name
      return renderTaskResponsStatusName(task)
    }

    if (isDraft && dealStatus === null) {
      return (
        <BaseButton
          children={words['user.subcontract.signAndPublish']}
          size="percent"
          className={styles.btn}
          onClick={handleSignTask}
        />
      )
    }

    if (dealStatus === null) {
      return <></>
    }

    const statusComponents = {
      // [ETaskStatus.DRAFT]: (
      //   <BaseButton
      //     children={words['user.subcontract.signAndPublish']}
      //     size="percent"
      //     className={styles.btn}
      //     onClick={handleSignTask}
      //   />
      // ),
      [EDealStatus.Created]: <div className={styles.created}>{words['TASK_STATUS_2']}</div>,
      [EDealStatus.Done]: <div className={styles.completed}>{words['TASK_STATUS_3']}</div>,
      // [ETaskStatus.AT_THE_INTERVIEW]: (
      //   <div className={styles.interwiev}>{words['TASK_STATUS_4']}</div>
      // ),
      [EDealStatus.InProgress]: <div className={styles.work}>{words['TASK_STATUS_5']}</div>,
      [EDealStatus.Canceled]: <div className={styles.canceled}>{words['TASK_STATUS_6']}</div>,
      [EDealStatus.CanceledByVoting]: (
        <div className={styles.canceled}>{words['TASK_STATUS_18'] || 'Cancelled by voting'}</div>
      ),
      [EDealStatus.StopedWithProblem]: (
        <div className={styles.canceled}>{words['TASK_STATUS_8']}</div>
      ),
      [EDealStatus.Accepted]: <div className={styles.canceled}>{words['TASK_STATUS_10']}</div>,
      [EDealStatus.Executed]: isCustomer ? (
        <BaseButton
          children={words['user.subcontract.acceptTask']}
          onClick={handleAcceptTask}
          size="percent"
          className={styles.btn}
        />
      ) : (
        <div className={styles.work}>{words['TASK_STATUS_9']}</div>
      ),

      [EDealStatus.Voting]: (
        <div className={styles.canceled}>{words['TASK_STATUS_16'] || 'Voting'}</div>
      ),
      [EDealStatus.VotingDone]: (
        <div className={styles.canceled}>{words['TASK_STATUS_17'] || 'Voting done'}</div>
      ),

      offer: !myAnswers && (
        <BaseButton
          children={words['user.taskSelection.suggest']}
          size="percent"
          className={styles['btn-offer']}
          onClick={(e: any) => {
            e.stopPropagation()
            openOfferPage()
          }}
        />
      ),
      planning: null
      // TO DO uncoment later
      // notMyAnswers && (
      //   <BaseButton
      //     children={
      //       task.tokenStatus
      //         ? words['user.subcontract.task.planning']
      //         : words['component.taskInfo.connectCalendar']
      //     }
      //     size="percent"
      //     type="submit"
      //     className={styles['btn-request']}
      //     onClick={(e: any) => {
      //       e.preventDefault()
      //       if (task.tokenStatus) {
      //         history.push(`${history.location.pathname}/planing-calendar?taskId=${task.id}`)
      //       } else {
      //         window.location.href = `${
      //           config.apiUrl
      //         }/google-calendar/refresh-token?token=${accessToken}&callbackUrl=${encodeURIComponent(
      //           window.location.href
      //         )}`
      //         dispatch(clearTaskList()) // Assuming clearTaskList action exists
      //       }
      //     }}
      //   />
      // )
    }

    return statusComponents[status as keyof typeof statusComponents] || null
    // return statusComponents[status as keyof typeof statusComponents] || null
  }

  return (
    <div className={styles.onetask} onClick={() => openCurrentTask()}>
      <div className={styles['info-section']}>
        <div className={styles['info-header']}>
          <h2>{task.title}</h2>
          <div>{renderStatusButton()}</div>
        </div>

        <div className={styles['info-container']}>
          <p>{task.description}</p>
          <div className={styles['meta-info']}>
            <div className={styles['task-number']}>{task.uuid}</div>
            {task.deadline ? (
              <p style={{ fontWeight: 'bold' }}>
                <Icons icon="calendarTask" /> {words['user.subcontract.until']}{' '}
                {dayjs(task.deadline)
                  .locale(currentLanguage)
                  .format('DD.MM.YYYY')}
              </p>
            ) : (
              <div style={{ opacity: '0.5', display: 'flex' }}>
                <p>
                  {dayjs(task.publishedAt || task.createdAt)
                    .locale(currentLanguage)
                    .fromNow()}
                </p>
                <p>
                  <Icons icon="popupDialog" />
                  {task.taskResponsesCount} {words['user.subcontract.requests']}
                </p>
              </div>
            )}

            <div className={styles['about-price']}>
              <h2
                className={cls({
                  [styles['price']]: true
                })}
              >
                {showPriceInfo()}
              </h2>
            </div>
          </div>

          {isOfferMe && (
            <div>
              <hr className={styles.hr} />
              <div className={styles['offer-title']}>
                {words['user.subcontract.taskRequest.offerYou'] ||
                  'You have been offered a task to complete:'}
              </div>
              <p className={styles.comment}>{isOfferMe.comment}</p>
              <div className={styles['offer-descr-container']}>
                <p className={styles['descr-item']}>
                  <span>{words['user.subcontract.taskRequest.budget']}:</span>
                  {` ${isOfferMe.budget} ${task.token && task.token.symbol}`}
                </p>
                {isOfferMe.deadline ? (
                  <p className={styles['descr-item']}>
                    <span>{words['user.subcontract.taskRequest.term']}:</span>
                    {` ${dayjs(isOfferMe.deadline).format('DD.MM.YYYY')}`}
                  </p>
                ) : null}
                <p className={styles['offer-date']}>{dayjs(isOfferMe.createdAt).fromNow()}</p>
              </div>
            </div>
          )}
        </div>
      </div>
      {/* <ProposeTaskModal toggle={isModalOpen} action={toggleModal} size={'md'} /> */}
    </div>
  )
}

export default TaskInfo
