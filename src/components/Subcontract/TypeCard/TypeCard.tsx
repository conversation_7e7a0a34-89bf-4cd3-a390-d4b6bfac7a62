import React from 'react'
import styles from './TypeCard.module.scss'
import cls from 'classnames'
import { ITypeCardProps } from 'screens/client/Subcontract/components/CreateTask/CreateTask.model'

export const TypeCard = ({ img, title, active, onCli }: ITypeCardProps) => {
  return (
    <div
      className={cls({ [styles['card-container']]: true, [styles['active']]: active })}
      onClick={() => {
        onCli()
      }}
    >
      {active ? <div className={styles.choosen} /> : null}
      <div style={{ display: 'flex' }}>
        <div className={cls({ [styles['opacity-active']]: active })} />
        <img src={img.src} alt={img.alt} className={styles['card-img']} />
        <div className={styles.text}>
          <h3>{title}</h3>
        </div>
      </div>
    </div>
  )
}
