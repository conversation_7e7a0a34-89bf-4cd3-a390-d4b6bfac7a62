import React, { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { TaskInfo } from '../TaskInfo'
import Spinner from 'components/Spinner'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import styles from './TasksList.module.scss'

interface ITasksListProps {
  lastFetchedPage: number
  fetchMoreData: () => Promise<void>
  isTransaction?: boolean
  taskStatus?: string
}

export const TasksList = ({
  lastFetchedPage,
  fetchMoreData,
  isTransaction,
  taskStatus
}: ITasksListProps) => {
  const tasks = useSelector((state: TState) => state.client.subcontract.tasks)
  const pageCount = useSelector((state: TState) => state.client.subcontract.pageCount)
  const [isPending, setIsPending] = useState(false)

  const fetchMore = async () => {
    setIsPending(true)
    await new Promise(resolve => setTimeout(() => resolve(true), 500))
    await fetchMoreData()
    setIsPending(false)
  }

  return (
    <>
      {isTransaction && <Spinner isBackground={true} isTransaction={isTransaction} />}
      <div className={styles['list-block']}>
        <InfiniteScroll
          dataLength={tasks.length}
          next={fetchMore}
          hasMore={!isPending && lastFetchedPage < pageCount}
          loader={<h4>Loading</h4>}
        >
          {tasks.map((task: any) => (
            <TaskInfo key={task.id} extraStatus={taskStatus} deadline={true} task={task} />
          ))}
        </InfiniteScroll>
      </div>
    </>
  )
}
