@import '../../../assets/style/colors';
@import '../../../assets/style/variables';
@import '../../../assets/style/mixins';

.container {
    @include container;
    .header {
      @include top-header;
      h1{
        @include top-header-h1
      }
    }
    margin: 0 0 25px;
    
}

.list-filter {
  display: flex;
  justify-content: space-between;
}

.filter {
  height: 521px;
  margin-left: 24px;
  position: sticky;
  top: 80px;

  .pagination-container {
    display: flex;
    justify-content: center
  }
}

.wrap-bar {
  position: relative;
  top: 16px;
  margin-bottom: 30px;
  display: flex;
  height: 40px;
  justify-content: space-between;
  .back-btn {
      font-size: 14px;
      cursor: pointer;
      color: $blue;
      background-color:  transparent;
      svg {
          position: relative;
          right: 5px;
          top: 3px;
      }
  }
}

.empty-list {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  h2 {
    font-size: 20px;
    line-height: 28px;
    color: $dark-grey;
  }
}
