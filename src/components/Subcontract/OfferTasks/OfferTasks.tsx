import React, { FC, useEffect, useState } from 'react'
import { useParams } from 'react-router'
import { useSelector } from 'react-redux'
import queryString from 'query-string'
import Pagination from 'rc-pagination'
import { TState } from 'store'

import styles from './OfferTasks.module.scss'
import '../../../assets/style/pagination.scss'
import { SubcontractFilter, TSubcontractFilterValues } from '../SubcontractFilter/SubcontractFilter'
import { ESubcontractTabs } from '../subcontract.enum'
import { Icons } from 'components/Icons'
import { TOfferTasksProps } from './OfferTasks.model'
import { ReactComponent as Prev } from 'assets/images/prev.svg'
import { ReactComponent as Next } from 'assets/images/next.svg'
import Spinner from 'components/Spinner'
import tasks_tablet from '../../../assets/images/tasks_tablet.svg'
import { finishPoint } from 'screens/client/Subcontract/components/CreateTask/initialvalues'
import { useQueryParams } from 'hooks/useQueryParams'
import { TasksList } from '../TasksList/TasksList'
import { TSelectOption } from 'components/Select'

const OfferTasks: FC<TOfferTasksProps> = ({
  history,
  getTasksForOffer,
  taskArray,
  itemsCount,
  loading,
  clearTaskList,
  tokens,
  fetchAllTokensThunk
}: any) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const parsed = useQueryParams()
  const queryParams: any = useParams()
  const candidatId = queryParams.candidatId
  const limit = 10
  const initPage = 1
  const parsedPageNum = +parsed.page || initPage

  const handleBack = () => {
    history.push('/dashboard/subcontract/performers?page=1')
  }

  const [currentPage, setCurrentPage] = useState(initPage)
  const [lastFetchedPage, setLastFetchedPage] = useState(initPage)
  useEffect(() => setLastFetchedPage(currentPage), [currentPage])

  useEffect(() => {
    if (!candidatId || isNaN(candidatId)) {
      handleBack()
      return
    }

    const search = queryString.stringify({
      ...parsed,
      page: parsedPageNum,
      limit
    })
    getTasksForOffer(search, candidatId, true, true)
    setCurrentPage(parsedPageNum)

    fetchAllTokensThunk()

    return () => {
      clearTaskList()
    }
  }, [])

  const resetFilter = () => {
    history.push({
      pathname: history.location.pathname,
      search: queryString.stringify({ page: initPage })
    })

    const search = queryString.stringify({ page: initPage, limit })
    getTasksForOffer(search, candidatId, true, true)
    setCurrentPage(initPage)
    setLastFetchedPage(initPage)
  }

  const changeFilter = (values: TSubcontractFilterValues) => {
    const selectValue = (select?: TSelectOption) =>
      select && select.value !== '@ALL@' ? select.value : undefined

    const searchParam = Object.fromEntries(
      Object.entries({
        page: initPage,
        category: selectValue(values.category),
        specialization: selectValue(values.specialization),
        level: selectValue(values.level),
        fromDate: values.publicationDateFrom,
        toDate: values.publicationDateTo,
        status: selectValue(values.status),
        currency: selectValue(values.currency),
        fromPrice: values.budgetFrom,
        toPrice: values.budgetTo
      }).filter(([_, value]) => !!value)
    )

    const searchStr = queryString.stringify(searchParam)
    history.push({
      pathname: history.location.pathname,
      search: searchStr
    })

    getTasksForOffer(`${searchStr}&limit=${limit}`, candidatId, true, true)
    setCurrentPage(initPage)
  }

  const handlePageChange = (pagePag: number) => {
    window.scrollTo(0, 0)
    const searchParam = { page: pagePag, limit }
    const search = `${queryString.stringify(searchParam)}&${finishPoint().trim()}`
    getTasksForOffer(search, candidatId, true, true)
    setCurrentPage(pagePag)

    history.push({
      pathname: history.location.pathname,
      search: queryString.stringify({ ...parsed, page: pagePag })
    })
  }

  const fetchMoreData = async () => {
    setLastFetchedPage(lastFetchedPage + 1)
    const searchParam = { page: lastFetchedPage + 1, limit }
    const search = `${queryString.stringify(searchParam)}&${finishPoint().trim()}`
    await getTasksForOffer(search, candidatId)
  }

  return (
    <div className={styles.container} style={{ width: '100%' }}>
      {loading && (
        <div className="spinnerBackDrop">
          <Spinner />
        </div>
      )}

      <section className={styles.header}>
        <h1>{words['user.header.subcontract']}</h1>
      </section>
      <div className={styles['wrap-bar']}>
        <button className={styles['back-btn']} onClick={handleBack}>
          <Icons icon={'arrowLeftWithStick'} />
          {words['back']}
        </button>
      </div>
      <div className={styles['list-filter']}>
        {taskArray.length === 0 && (
          <div className={styles['empty-list']}>
            <img src={tasks_tablet} alt="logo" />
            <h2>{words['user.subcontract.taskListIsEmpty']}</h2>
          </div>
        )}
        {taskArray.length > 0 && (
          <TasksList
            lastFetchedPage={lastFetchedPage}
            fetchMoreData={fetchMoreData}
            taskStatus="offer"
          />
        )}
        <div className={styles.filter}>
          <SubcontractFilter
            tokens={tokens}
            activeTab={ESubcontractTabs.ALL_TASKS}
            resetFilter={resetFilter}
            onSubmit={changeFilter}
          />

          <div className={styles['pagination-container']}>
            <Pagination
              defaultPageSize={limit}
              total={itemsCount}
              current={parsedPageNum}
              onChange={handlePageChange}
              hideOnSinglePage={true}
              prevIcon={<Prev />}
              nextIcon={<Next />}
              className={'rc-pagination'}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default OfferTasks
