import React, { startTransition } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { NavLink } from 'react-router-dom'
import { TState } from 'store'
import styles from './PerformerInfo.module.scss'
import { ReactComponent as DefaultAvatar } from 'assets/images/default_avatar.svg'
import { BaseButton } from 'components/UiKit/Button/BaseButton'
import { useHistory } from 'react-router'
import { clearTaskList } from 'screens/client/Subcontract/Subcontract.actions'

export interface IUserPerformer {
  id: number
  photo: string | null
  fullName: string
  technologies: string | null
  position: { id: number; name: string } | null
  rank: { id: number; name: string } | null
  countPositiveReviews: number | null
  countNegativeReviews: number | null
}

interface IPerformerInfoProps {
  user: IUserPerformer
}

export const PerformerInfo = ({ user }: IPerformerInfoProps) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const history = useHistory()

  const dispatch = useDispatch()

  return (
    <NavLink
      exact={true}
      className={styles.onetask}
      to={`/dashboard/foreign-profile?userId=${user.id}&profileType=main-info&type=goods`}
    >
      <div className={styles['info-section']}>
        <div className={styles['user-info']}>
          <div className={styles['user']}>
            <div>
              {user && user.photo ? (
                <img className={styles.avatar} src={user.photo} alt="avatar" />
              ) : (
                <DefaultAvatar className={styles.avatar} />
              )}
            </div>
            <div className={styles['user-desc']}>
              <h2>{user.fullName}</h2>
              <p>
                {user.position ? words[`${user.position.name}`] : '-'}
                {' / '}
                {user.rank ? words[`${user.rank.name}`] : '-'}
              </p>
              <p className={styles.score}>
                {words['user.subcontract.rating']}:
                <span className={styles.positive}>{user.countPositiveReviews}</span>/
                <span className={styles.negative}>{user.countNegativeReviews}</span>
              </p>
            </div>
          </div>
          <BaseButton
            children={words['user.subcontract.suggestTask']}
            size="percent"
            type="button"
            onClick={e => {
              dispatch(clearTaskList())
              e.preventDefault()
              startTransition(() => {
                history.push(`/dashboard/subcontract/offer-task/${user.id}?page=1`)
              })
            }}
          />
        </div>
        <div className={styles['description']}>
          <p>
            <span>{words['user.subcontract.task.technology']}: </span>
            <br />
            {user.technologies || 'No technology for this user'}
          </p>
        </div>
      </div>
    </NavLink>
  )
}
