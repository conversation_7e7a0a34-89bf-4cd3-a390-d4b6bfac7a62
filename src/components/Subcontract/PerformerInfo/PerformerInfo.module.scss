@import '../../../assets/style/colors';
@import '../../../assets/style/mixins';

.info-section, .filesSection {
  @include profile-info-section;
  cursor: pointer;
  width: 100%;
  margin: 0;

  .user-info {
    display: flex;
    justify-content: space-between;
    column-gap: 40px;

    .avatar {
      width: 78px;
      height: 78px;
      margin-right: 5px;
      border-radius: 100%;
    }

    .user {
      display: flex;
      column-gap: 12px;

      .user-desc {
        text-align: left;
        padding-top: 10px;
        margin-bottom: 25px;

        h2 {
          margin-bottom: 4px;
          color: $blue;
        }

        p {
          margin: 4px 0px;
        }

        .score {
          font-weight: 700;
          font-size: 12px;
          color: $grey;

         .positive {
           color: #149B58; // green
           margin-left: 8px;
         }
         .negative {
           color: #E12F75; // red
           margin-left: 4px;
         }
        }
      }
    }

    button {
      width: 160px;
      font-size: 14px;
      cursor: pointer;
      padding: 0px;
      border-radius: 10px;
    }
  }

  .description {
    display: flex;
    background-color: #fff;
    line-height: 24px;
    text-align: left;
    justify-content: space-between;
    font-weight: 400;
    font-size: 14px;

    span {
      color: $dark-grey;
      font-weight: 700;
      font-size: 14px;
    }
  }
}

.onetask {
  text-decoration: none;
  color: $dark-grey;
  width: 100%;
  display: inline-flex;
  margin-bottom: 24px;
}
