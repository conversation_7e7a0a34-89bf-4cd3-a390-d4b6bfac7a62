import React from 'react'
// import styles from './PerformersList.module.scss'
import { IUserPerformer, PerformerInfo } from '../PerformerInfo/PerformerInfo'
// import InfiniteScroll from 'react-infinite-scroll-component'
import styles from './PerformersList.module.scss'

interface IPerformersListProps {
  performers: IUserPerformer[]
  // getPerformers: (value: number) => void
  // totalPages: number
}

// getPerformers, totalPages
export const PerformersList = ({ performers }: IPerformersListProps) => {
  // const urlSearchParamses = new URLSearchParams(window.location.search)
  // const paramses = Object.fromEntries(urlSearchParamses.entries())

  // const newPage = (paramses.page ? +paramses.page : 0) + 1

  // const fetchMore = () => {
  // const url = new URL((window.location as unknown) as string)
  // url.searchParams.set('page', String(newPage))
  // window.history.pushState({}, '', url.toString())
  // getPerformers(newPage)
  // window.scrollTo(0, 0)
  // }

  return (
    <div className={styles.container}>
      {/* <InfiniteScroll
          dataLength={performers.length}
          next={fetchMore}
          // hasMore={newPage <= totalPages}
          hasMore={false}
          loader={<h4>Loading...</h4>}
        > */}
      {performers.map((user: any) => (
        <PerformerInfo key={user.id} user={user} />
      ))}
      {/* </InfiniteScroll> */}
    </div>
  )
}
