import React, { FC, useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Field, Form } from 'react-final-form'
import { TState } from 'store'
import toastr from '../../utils/toastr'
import cls from 'classnames'
import validation from '../../utils/validation'
import { Button } from '../UiKit/Button'
import { RedNote } from '../RedNote'
import { Textarea } from '../Textarea'
import Modal from '../Modal'
import styles from './styles.module.scss'
import { EDealStatus } from 'screens/client/Web3/web3.model'
import { SplitSumRangeInput } from 'components/SplitSumRangeInput'
import Checkbox from 'components/UiKit/Checkbox/Checkbox'
import { Dispatch } from 'redux'
import { TCancelTaskModal } from 'screens/client/Subcontract/components/Task/Task.model'
import {
  cancelTaskThunk,
  handleStopTaskThunk,
  stopTaskWithProblemByAdminThunk
} from 'screens/client/Web3/web3.thunk'
import { setOpenInfoModal } from 'screens/client/Web3/web3.action'
import exclamationMark from 'assets/images/exclamation-mark.svg'
import { checkTaskCancellation } from 'screens/client/Subcontract/Subcontract.thunk'

type TModalProps = {
  action: (v: TCancelTaskModal) => void
  modalType: TCancelTaskModal
  size?: 'default' | 'md'
  task: any
  loading: boolean
  error: string
  currentUser: any
  proposal: any
  isAdmin: boolean
  createVoting?: (endOfVoting: Date, taskId: number) => (dispatch: Dispatch) => Promise<void>
}

export interface InputsState {
  field1: number
  field2: number
  numInRange: number
}

const CancelTaskModal: FC<TModalProps> = ({
  action,
  modalType,
  size = 'default',
  task,
  loading,
  error,
  currentUser,
  createVoting,
  proposal,
  isAdmin
}) => {
  const [isChecked, setIsChecked] = useState<any>(
    modalType === 'cancellation-with-dividening-sum' ? true : false
  )
  const [isOnVoting, setIsOnVoting] = useState(false)
  const words = useSelector((state: TState) => state.global.language.words)
  const deal = useSelector((state: TState) => state.client.deal.data)
  const dealStatus = deal ? +deal.status : null
  const isInfoModalOpen = useSelector((state: TState) => state.client.web3.isInfoModalOpen)
  const isTaskCancellation = useSelector(
    (state: TState) => state.client.subcontract.taskCancellation
  )

  const dispatch = useDispatch()
  const [inputsState, setInputsState] = useState<InputsState>({
    field1: 0,
    field2: 0,
    numInRange: 50
  })

  useEffect(() => {
    dispatch(checkTaskCancellation(task.id))
  }, [task])

  const isCustomer = useMemo(() => {
    return task.customer.id === currentUser.id
  }, [task, currentUser])

  const { taskOnVoting, isStopTask, refundPercent } = useMemo(() => {
    let voting = false
    let isCancellationInfo = false
    let refund = 0
    const taskCancellation = task.taskCancellation

    if (taskCancellation) {
      if (taskCancellation.publicVoting) {
        voting = true
      }
      if (dealStatus === EDealStatus.StopedWithProblem) {
        isCancellationInfo = true
      }

      if (isCustomer) {
        refund = 100 - taskCancellation.refundContractorPercent
      } else {
        refund = 100 - taskCancellation.refundCustomerPercent
      }
    }
    if (task.dealStatus === EDealStatus.Voting) {
      voting = true
    }
    return {
      taskOnVoting: voting,
      isStopTask: isCancellationInfo,
      refundPercent: refund
    }
  }, [task])

  const textCancellation = useMemo(() => {
    const text =
      words['user.subcontract.completeTaskChoosePercent'] ||
      'To complete the task you need to choose <percent>% in your favor.'
    return text.replace('<percent>', `${refundPercent}`)
  }, [refundPercent])

  const onSubmit = async (values: any) => {
    if (isAdmin) {
      await dispatch(
        stopTaskWithProblemByAdminThunk(values, task, inputsState.numInRange, currentUser.id)
      )
    } else if (!isChecked && !isAdmin) {
      await dispatch(
        cancelTaskThunk(values, modalType, task, currentUser.id, isCustomer, isTaskCancellation)
      )
    } else {
      await dispatch(
        handleStopTaskThunk(
          values,
          task,
          currentUser.id,
          isCustomer,
          inputsState.numInRange,
          isOnVoting,
          isTaskCancellation,
          createVoting
        )
      )
    }
  }

  const closeLoadingModal = () => {
    dispatch(setOpenInfoModal(false))
  }

  const checkHandler = () => {
    setIsChecked(!isChecked)
    if (isChecked) {
      setIsOnVoting(false)
    }
  }

  const onChangeVoting = () => {
    setIsOnVoting(!isOnVoting)
    if (!isOnVoting) {
      setIsChecked(true)
    }
  }

  const isShowSplitSum = (isChecked && !isAdmin) || isAdmin

  const splitSumComments = useMemo(() => {
    const arr: { text: string; value: number; color?: string }[] = []
    if (!isShowSplitSum) return arr

    if (isAdmin) {
      arr.push({
        text: words['user.subcontract.inCustomerFavor'] || 'In favor of the customer',
        value: inputsState.numInRange
      })
      arr.push({
        text: words['user.subcontract.inPerformerFavor'] || 'In favor of the performer',
        value: 100 - inputsState.numInRange,
        color: 'red'
      })
    } else {
      arr.push({
        text: words['user.subcontract.inMyFavor'] || 'In my favor',
        value: inputsState.numInRange
      })
    }

    return arr
  }, [isShowSplitSum, isAdmin, inputsState.numInRange])

  const getBtnsJsx = (clickHandler: () => void) => {
    return (
      <section className={`${styles.buttons} ${styles['buttons-cancel']}`}>
        <Button outline={true} size={'lg'} onClick={() => action('')}>
          <span>{words['component.notifications.close']}</span>
        </Button>
        <Button onClick={clickHandler} size={'lg'}>
          <span>{words['user.subcontract.cancelTaskModal.cancelTask']}</span>
        </Button>
      </section>
    )
  }

  return (
    <div>
      <div onClick={() => action('')} className={cls({ [styles.overlay]: true })} />
      <div className={cls({ [styles.container]: true, [styles.active]: true })}>
        <div className={cls({ [styles.modal]: true, [styles[`modal-size-${size}`]]: true })}>
          <h2>{words['user.subcontract.cancelTaskModal.header']}</h2>{' '}
          {modalType === 'confirm-cancellation' ? (
            // Confirm cancelation
            <>
              <div className={styles.message}>
                {words['user.subcontract.confirmingCancellation'] ||
                  'By confirming the cancellation you agree that the entire amount will be returned to the customer.'}
              </div>
              {getBtnsJsx(() =>
                dispatch(
                  cancelTaskThunk(
                    { cancelDescription: '' },
                    'confirm-cancellation',
                    task,
                    currentUser.id,
                    isCustomer,
                    isTaskCancellation
                  )
                )
              )}
            </>
          ) : (
            <>
              {dealStatus !== EDealStatus.Created && (
                <h3 className={styles.subTitle}>
                  {isChecked
                    ? words['user.cancelTask.modal.subTitleFirst']
                    : words['user.cancelTask.modal.subTitleSecond']}
                </h3>
              )}
              <Form
                onSubmit={(values: any) => {
                  onSubmit(values)
                }}
              >
                {({ form, handleSubmit }) => {
                  const formState = form.getState()
                  const checkRequired = async () => {
                    if (formState.errors && Object.keys(formState.errors).length !== 0) {
                      handleSubmit()
                      toastr('error', words['user.editProfile.fillRequiredFields'])
                    } else {
                      handleSubmit()
                      action('')
                    }
                  }

                  return (
                    <form onSubmit={handleSubmit} name="form">
                      <section
                        className={cls({
                          [styles.form]: true,
                          [styles['cancel-task-form']]: true
                        })}
                      >
                        <div className={styles.message}>
                          <div className={styles.label}>
                            <label>
                              {words['user.profile.table.comment']} {<RedNote />}
                            </label>
                          </div>
                          <Field
                            name="cancelDescription"
                            validate={validation.required(words['user.requiredMessage'])}
                          >
                            {({ input, meta }) => (
                              <Textarea
                                {...input}
                                isInvalid={meta.error && meta.submitFailed}
                                errorMessage={meta.error}
                                size={'sm'}
                                placeholder={words['user.cancelTask.modal.placeholder']}
                              />
                            )}
                          </Field>
                        </div>

                        {(dealStatus === EDealStatus.InProgress ||
                          dealStatus === EDealStatus.RequestCancelByClient ||
                          dealStatus === EDealStatus.RequestCancelByContractor ||
                          dealStatus === EDealStatus.Executed ||
                          modalType === 'cancellation-with-dividening-sum') && (
                          <div
                            className={cls(styles.check, {
                              [styles['check--top']]: isChecked || isOnVoting
                            })}
                          >
                            <div
                              className={cls([
                                styles.block,
                                { [styles['admin-content']]: isAdmin }
                              ])}
                            >
                              {!isAdmin && (
                                <Checkbox
                                  name={'refund'}
                                  value={isChecked}
                                  checked={isChecked}
                                  label={words['user.cancelTask.modal.checkBoxDevide']}
                                  onChange={checkHandler}
                                  disabled={modalType === 'cancellation-with-dividening-sum'}
                                />
                              )}
                              {isShowSplitSum && (
                                <div
                                  className={cls([
                                    styles.splitSumWrapper,
                                    { [styles['admin-content']]: isAdmin }
                                  ])}
                                >
                                  <SplitSumRangeInput
                                    budget={proposal && proposal.budget}
                                    inputsState={inputsState}
                                    setInputsState={setInputsState}
                                    task={task}
                                    words={words}
                                  />
                                  <div className={styles.splitSumCommentBlock}>
                                    {splitSumComments.map((data, idx) => (
                                      <div key={idx} className={styles.splitSumComment}>
                                        {data.text}:
                                        <span
                                          className={cls([
                                            styles.splitSumCommentPercent,
                                            { [styles[data.color || '']]: data.color }
                                          ])}
                                        >
                                          {data.value}%
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>

                            {!isAdmin && (
                              <div className={styles.block}>
                                {!taskOnVoting && (
                                  <Checkbox
                                    name={'voting'}
                                    value={isOnVoting as any}
                                    checked={isOnVoting}
                                    label={words['user.cancelTask.modal.checkBoxVote']}
                                    onChange={onChangeVoting}
                                  />
                                )}

                                {isShowSplitSum && (
                                  <div className={styles.descrWrapper}>
                                    {!taskOnVoting && (
                                      <div className={styles.controllerDescription}>
                                        <img src={exclamationMark} alt="attention" />
                                        <div className={styles.controllerDescription__description}>
                                          {words['user.cancelTask.modal.description']}
                                        </div>
                                      </div>
                                    )}

                                    {isStopTask && refundPercent !== 100 && (
                                      <div className={styles.controllerDescription}>
                                        <img src={exclamationMark} alt="attention" />
                                        <div className={styles.controllerDescription__description}>
                                          {textCancellation}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </section>

                      {getBtnsJsx(checkRequired)}
                    </form>
                  )
                }}
              </Form>
            </>
          )}
        </div>
      </div>

      <Modal
        isShow={isInfoModalOpen}
        onClose={() => {
          if (!loading) closeLoadingModal()
        }}
        className={styles.LoadingModal}
      >
        {loading && !error && <p>{words['user.subcontract.taskRequest.loading']}</p>}
        {!loading && !error && (
          <div className={styles.success}>
            <p>{words['admin.subcontract.taskCancel.success']}</p>
            <div className={styles.btn}>
              <Button onClick={closeLoadingModal}>
                <span>Ok</span>
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default CancelTaskModal
