import React, { FC } from 'react'
import { useSelector } from 'react-redux'

import Modal from '../Modal'
import styles from './styles.module.scss'
import { TState } from 'store'
import { Button } from 'components/UiKit/Button'

interface IModalProps {
  openModalCancelDraftTask: boolean
  setOpenModalCancelDraftTask: (value: boolean) => void
  cancelDraftTask: () => void
}

const CancelDraftTaskModal: FC<IModalProps> = ({
  openModalCancelDraftTask,
  setOpenModalCancelDraftTask,
  cancelDraftTask
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <Modal
      className={styles['cancel-draft-modal']}
      isShow={openModalCancelDraftTask}
      onClose={() => setOpenModalCancelDraftTask(false)}
    >
      <h2>{words['user.subcontract.cancelTaskModal.header']}</h2>

      <div className={styles.content}>
        {words['user.subcontract.taskWillBeCancelled'] || 'The task will be cancelled.'}
      </div>

      <div className={styles.buttons}>
        <Button onClick={() => setOpenModalCancelDraftTask(false)} outline={true} size={'lgs'}>
          {words['user.subcontract.cancelTaskModal.cancel']}
        </Button>
        <Button onClick={cancelDraftTask} size={'lgs'}>
          {words['user.subcontract.cancelTaskModal.cancelTask']}
        </Button>
      </div>
    </Modal>
  )
}

export default CancelDraftTaskModal
