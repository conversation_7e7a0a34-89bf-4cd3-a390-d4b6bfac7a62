import React, { FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { TState } from 'store'
import { Field, Form } from 'react-final-form'
import toastr from 'utils/toastr'
import validation from 'utils/validation'
import { getTrlBalanceThunk } from 'store/modules/balance/balance.thunk'
import { buyBidsThunk, getBidPriceThunk } from './PurchaseBidsModal.thunk'
import { Input } from 'components/UiKit/Inputs'
import { Button } from 'components/UiKit/Button'
import Spinner from 'components/Spinner'
import Modal from 'components/Modal'
import { EMessageCodes } from 'types/EMessageCodes'

import styles from './PurchaseBidsModal.module.scss'

type TModalProps = {
  onClose: () => void
}

const PurchaseBidsModal: FC<TModalProps> = ({ onClose }) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const dispatch = useDispatch()
  const defaultAmoundBids = 1

  const [loadingInit, setLoadingInit] = useState(false)
  const [loadingSubmit, setLoadingSubmit] = useState(false)
  const [price, setPrice] = useState(0)
  const [lot, setLot] = useState(0)

  const onInitComponent = async () => {
    try {
      setLoadingInit(true)
      const value = await getBidPriceThunk()
      setPrice(value)
      setLot(value * defaultAmoundBids)
    } catch (error) {
      const err = error as Error
      console.error(err)
      const msg =
        words[err.message] ||
        (words[EMessageCodes.FAILED_LOAD_BID_PRICE] || 'Something went wrong loading bid price')
      toastr('error', msg)
    } finally {
      setLoadingInit(false)
    }
  }

  const onSubmitHandler = async (values: { amount: string }) => {
    try {
      setLoadingSubmit(true)
      if (price <= 0) throw new Error('Price is zero')
      const amount = Number(values.amount)
      await dispatch(buyBidsThunk(amount, price))
      await dispatch(getTrlBalanceThunk())
    } catch (error) {
      const err = error as Error
      console.error(err)
      const msg =
        words[err.message] ||
        (words[EMessageCodes.FAILED_BID_PURCHASE] || 'Something went wrong with the bid purchase')
      toastr('error', msg)
    } finally {
      setLoadingSubmit(false)
      onClose()
    }
  }

  useEffect(() => {
    onInitComponent()
  }, [])

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const valueNumber = Number(value)

    if (Number.isInteger(valueNumber)) {
      setLot(valueNumber * price)
    }
  }

  return (
    <Modal className={styles['modal']} isShow={true} onClose={onClose}>
      <div className={styles['container']}>
        <h2 className={styles['title']}>{words['user.subcontract.purchaseBidsModal.header']}</h2>
        <Form onSubmit={onSubmitHandler} initialValues={{ amount: defaultAmoundBids.toString() }}>
          {({ form, handleSubmit }) => {
            const formState = form.getState()
            const checkRequired = () => {
              if (formState.errors && Object.keys(formState.errors).length !== 0) {
                const msg = words['user.main.form.fillRequiredFields'] || 'Fill required fields!'
                toastr('error', msg)
              } else {
                handleSubmit()
              }
            }
            return (
              <form onSubmit={handleSubmit} name="form">
                <section className={styles['form']}>
                  <div className={styles['amount']}>
                    <div className={styles['label']}>
                      <label>{words['user.subcontract.purchaseBidsModal.amount']}</label>
                    </div>
                    <Field
                      name="amount"
                      validate={validation.composeValidators(
                        validation.required(),
                        validation.isInteger(),
                        validation.min(1)
                      )}
                    >
                      {({ input, meta }) => {
                        return (
                          <Input
                            {...input}
                            onChange={e => {
                              input.onChange(e)
                              onChange(e)
                            }}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            variant={'outlined'}
                          />
                        )
                      }}
                    </Field>
                  </div>
                  <div className={styles['price']}>
                    <span>{words['user.subcontract.price']}: </span>
                    <span>{lot} TRL</span>
                  </div>
                </section>
                <section className={styles['buttons']}>
                  <Button outline={true} size={'lg'} onClick={onClose}>
                    <span>{words['user.subcontract.cancelTaskModal.cancel']}</span>
                  </Button>
                  <Button onClick={checkRequired} size={'lg'} disabled={price <= 0}>
                    <span>{words['user.subcontract.taskRequest.buy']}</span>
                  </Button>
                </section>
              </form>
            )
          }}
        </Form>
        {(loadingInit || loadingSubmit) && (
          <div className={styles['loader-block']}>
            <Spinner />
          </div>
        )}
      </div>
    </Modal>
  )
}

export default PurchaseBidsModal
