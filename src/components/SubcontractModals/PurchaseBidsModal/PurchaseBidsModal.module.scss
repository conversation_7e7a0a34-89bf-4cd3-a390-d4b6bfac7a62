@import 'assets/style/colors';
@import 'assets/style/variables';
@import 'assets/style/mixins';

.modal {
  overflow: hidden;
  min-height: unset;
}

.container {
  margin-top: 12px;
}

.title {
  font-size: 20px;
  line-height: 28px;
  margin: 0 0 24px;
  text-align: center;
}

.form {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: flex-end;
  text-align: left;
}

.label {
  font-size: $font-size-xs;
  color: $grey;
  margin-bottom: 4px;
}

.amount {
  input {
    width: 200px;
  }
}

.price {
  font-weight: bold;
  color: $dark-grey;
  line-height: 30px;

  span:nth-child(2) {
    color: $green;
  }
}

.buttons {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.loader-block {
  position: absolute;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.35);
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
