/* eslint-disable prettier/prettier */
import { TState } from 'store'
import { EContractAddress } from 'globalConfigs'
import { checkWSEventListeners } from 'screens/client/Web3/web3.thunk'
import { pulling } from 'screens/auth/Login/Login.thunx'
import { checkWalletAddressWagmi } from 'helpers/checkWalletAddress'
import { EMessageCodes } from 'types/EMessageCodes'
import { getContractService } from 'wagmiContractService'
import { signPermitTypedData } from 'utils/signPermitTypedData'


export const checkRequiredDataWagmi = async (data: TState) => {
  const { address, signer, client, isRightWallet } = await checkWalletAddressWagmi(data)
  
  if (!isRightWallet) throw new Error(EMessageCodes.ERR_NOT_RIGHT_WALLET_ADDRESS)

  const { taskEvents } = await checkWSEventListeners()
  if (!taskEvents) throw new Error(EMessageCodes.BLOCKCHAIN_CONNECTION_WARNING)

  return { client, signer, address }
}

export async function getBidPriceThunk() {
   const { read } = await getContractService(
    EContractAddress.FDEscrowDiamond,
    EContractAddress.FDBidFacet
  )
  
  const price = (await read('getBidPrice', [EContractAddress.TRL_TOKEN])) as bigint
  return Number(price) / Math.pow(10, 9)
}

export const buyBidsThunk = (amount: number, price: number) => async (
  dispatch: any,
  getData: () => TState
) => {
  const { client, signer, address } = await checkRequiredDataWagmi(getData())
  

  const contract = await getContractService(
    EContractAddress.FDEscrowDiamond,
    EContractAddress.FDBidFacet
  )

  const token = await getContractService(EContractAddress.TRL_TOKEN)

  const nonce = ((await token.read('nonces', [address])) as unknown) as bigint
  
  const deadline = Math.floor(Date.now() / 1000) + 3600 // + 1 hour
  const name = (await token.read('name')) as string
  const chainId = client?.chain.id as number

  const domain = {
    name,
    version: '1',
    chainId,
    verifyingContract: token.address as `0x${string}`
  }

  const types = {
    Permit: [
      { name: 'owner', type: 'address' },
      { name: 'spender', type: 'address' },
      { name: 'value', type: 'uint256' },
      { name: 'nonce', type: 'uint256' },
      { name: 'deadline', type: 'uint256' }
    ]
  }

  const message = {
    owner: address,
    spender: contract.address,
    value: BigInt(amount * (price * Math.pow(10, 9))),
    nonce: nonce,
    deadline
  }
 
  const { r, s, v } = await signPermitTypedData({
    signer,
    account: address as `0x${string}`,
    domain,
    types,
    message
  })
  
  const args = [amount, token.address, deadline, v, r, s]
  

  const hash = await contract.writeAndWait('buyBidWithPermit', args, address as `0x${string}`)

  console.log(`Transaction hash: ${hash}`)
  await dispatch(pulling())
}
