import React, { ChangeEvent, FC, useEffect, useMemo, useState, MouseEvent } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { TState } from 'store'
import cls from 'classnames'
import FieldFactory from 'components/UiKit/FieldFactory/FieldFactory'
import styles from './styles.module.scss'
import { Icons } from '../Icons/'
import { IToken } from 'screens/admin/Token/Token.model'
import Button from 'components/Button'
import Spinner from 'components/Spinner'
import { fetchOneToken } from 'screens/admin/Token/Token.thunx'
import { ETokenStatus } from 'screens/admin/Token/Token.constans'
import toastr from 'utils/toastr'
import { TokenChoseModal } from './TokenChoseModal'
import { addTokenRequest } from 'screens/admin/Token/Token.actions'

type TModalProps = {
  toggle?: boolean
  action: () => void
  size?: 'default' | 'md' | 'small'
  tokens: {
    adminActivatedTokens: IToken[]
    adminDeactivatedTokens: IToken[]
    userActivatedTokens: IToken[]
    userDeactivatedTokens: IToken[]
  }
  tokensLoading?: boolean
  addToken: (token: IToken) => string
  fetchOneToken: (value: string) => IToken
  changeUserTokenStatusThunk: (status: ETokenStatus, token: IToken) => Promise<void>
  getSelectedToken: (value: IToken) => void
  title?: string
  button?: string
  isManageTokens?: boolean
}

const TokenChangeModal: FC<TModalProps> = ({
  toggle,
  size = 'small',
  tokens,
  action,
  changeUserTokenStatusThunk,
  addToken,
  getSelectedToken,
  title = '',
  button = '',
  isManageTokens
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const loadingToken = useSelector((state: TState) => state.admin.token.loadingToken)

  const [openTokenEditModal, setOpenTokenEditModal] = useState<boolean>(false)
  const [valueSearchToken, setValueSearchToken] = useState('')
  const [currentToken, setCurrentToken] = useState<IToken | null>(null)

  const dispatch = useDispatch()

  const {
    userActivatedTokens,
    userDeactivatedTokens,
    adminActivatedTokens,
    adminDeactivatedTokens
  } = tokens

  const currentUserTokens = useMemo(() => {
    const userTokens = []
    if (userActivatedTokens) {
      userTokens.push(...userActivatedTokens)
    }
    if (userDeactivatedTokens) {
      userTokens.push(...userDeactivatedTokens)
    }

    return userTokens
  }, [userActivatedTokens, userDeactivatedTokens])

  const handleOpenEditModal = (): void => {
    if (isManageTokens) {
      setOpenTokenEditModal(true)
    }
  }

  const handleChangeTokenStatus = (status: ETokenStatus, token: IToken): void => {
    changeUserTokenStatusThunk(status, token)
  }

  const isActiveUserToken = useMemo(() => {
    const activeUserTokens = userActivatedTokens.map(token => token.id)

    return (tokenId: number): boolean => {
      return activeUserTokens.includes(tokenId)
    }
  }, [userActivatedTokens])

  const handleSearchInput = (event: ChangeEvent<HTMLInputElement>): void => {
    setValueSearchToken(event.target.value)
  }

  const findToken = async (value: string) => {
    setCurrentToken(null)

    try {
      dispatch(addTokenRequest(true))
      const token = await fetchOneToken(value)

      if (token) {
        const findedUserToken = currentUserTokens.find(item => item.address === token.address)
        const findedAdminToken = [...adminActivatedTokens, ...adminDeactivatedTokens].find(
          item => item.address === token.address
        )

        if (findedUserToken) {
          toastr('warning', words['user.subcontract.tokenAlreadyExists'] || 'Token already exists')
        } else if (findedAdminToken && !findedAdminToken.isActive) {
          const text =
            words['user.subcontract.tokenDeactivatedByAdmin'] ||
            'Token <name> has been deactivated by administrator'
          const normText = text.replace('name', findedAdminToken.name)
          toastr('warning', normText)
        } else {
          setCurrentToken(token)
        }
      } else {
        toastr('warning', words['user.subcontract.tokenNotFound'] || 'Token not found')
      }
    } catch (error) {
      console.log('tokenChangeModal error', error)
      toastr('error', words['user.subcontract.tokenSearchError'] || 'Token search error')
    } finally {
      dispatch(addTokenRequest(false))
    }
  }

  const handleAddToken = async (event: object): Promise<void> => {
    const mouseEvent = event as MouseEvent
    mouseEvent.preventDefault()

    try {
      if (currentToken) {
        const res = await addToken({ ...currentToken })

        if (res && res === 'ok') {
          setCurrentToken(null)
          toastr('success', words['admin.settings.tokenImported'])
        } else {
          throw new Error(res)
        }
      }
    } catch (err) {
      console.log('error', err)
    }
  }

  useEffect(() => {
    if (!valueSearchToken) {
      setCurrentToken(null)
      return
    }

    const timeoutId = setTimeout(() => {
      findToken(valueSearchToken)
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [valueSearchToken])

  return (
    <div>
      <div onClick={action} className={cls({ [styles.overlay]: toggle })} />
      <div className={cls({ [styles.container]: true, [styles.active]: toggle })}>
        <div className={cls({ [styles.modal]: true, [styles[`modal-size-${size}`]]: true })}>
          {!openTokenEditModal ? (
            <TokenChoseModal
              tokens={tokens}
              handleOpenEditModal={handleOpenEditModal}
              action={action}
              getSelectedToken={getSelectedToken}
              title={title}
              button={button}
            />
          ) : (
            <div className={styles.modalWrapper}>
              <div>
                <div className={styles.modalTitle}>{words['admin.settings.tokenManagement']}</div>
                <div className={styles.closeModalButton} onClick={action}>
                  <Icons icon="close" />
                </div>
                <div className={styles.returnButton} onClick={() => setOpenTokenEditModal(false)}>
                  <Icons icon="backArrowIcon" />
                </div>
                <div
                  className={cls({
                    [styles.form]: true,
                    [styles['cancel-task-form']]: true
                  })}
                >
                  <div className={styles.message}>
                    <FieldFactory
                      config={[
                        {
                          items: [
                            {
                              name: `tokenSearch`,
                              label: '',
                              required: false,
                              inputWrapperClassName: styles['currency'],
                              component: () => {
                                return {
                                  type: 'input',
                                  props: {
                                    placeholder:
                                      words['user.subcontract.tokenChangeModal.searchName'],
                                    options: '',
                                    variant: 'outlined',
                                    value: valueSearchToken,
                                    onChange: handleSearchInput
                                  }
                                }
                              }
                            }
                          ]
                        }
                      ]}
                      words={words}
                    />
                  </div>
                </div>
                <div className={styles.tokenManageList}>
                  {loadingToken && (
                    <div className={styles.spinnerWrapper}>
                      <Spinner size="button" />
                    </div>
                  )}

                  {currentToken && (
                    <div className={styles.tokenItemContent}>
                      <div className={styles.tokenItem}>
                        <img
                          src={currentToken.logoURI}
                          alt="Token image"
                          className={styles.tokenImage}
                        />

                        <div className={styles.tokenItemWrapper}>
                          <div className={styles.tokenItemTitle}>{currentToken.symbol}</div>
                          <div className={styles.tokenItemDescription}>{currentToken.name}</div>
                        </div>
                      </div>

                      <Button
                        className={'btn-import'}
                        children={words['admin.settings.import']}
                        onClick={handleAddToken}
                      />
                    </div>
                  )}

                  {!currentToken &&
                    !loadingToken &&
                    currentUserTokens.map(token => (
                      <div key={token.address} className={styles.tokenItemContent}>
                        <div className={styles.tokenItem}>
                          <img
                            src={token.logoURI}
                            alt="Token image"
                            className={cls({
                              [styles.tokenImage]: true,
                              [styles['gray-img']]: !isActiveUserToken(token.id)
                            })}
                          />

                          <div
                            className={cls({
                              [styles.tokenItemWrapper]: true,
                              [styles['is-activated']]: !isActiveUserToken(token.id)
                            })}
                          >
                            <div className={styles.tokenItemTitle}>{token.symbol}</div>
                            <div className={styles.tokenItemDescription}>{token.name}</div>
                          </div>
                        </div>

                        {isActiveUserToken(token.id) ? (
                          <div
                            className={styles.deactivatedButton}
                            onClick={() => handleChangeTokenStatus(ETokenStatus.DEACTIVATE, token)}
                          >
                            <Icons icon="redBucket" />
                          </div>
                        ) : (
                          <div
                            className={styles.activatedButton}
                            onClick={() => handleChangeTokenStatus(ETokenStatus.ACTIVATE, token)}
                          >
                            <Icons icon="restoreIcon" />
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>

              <div className={styles.totalTokens}>{`${words['user.subcontract.tokensImported'] ||
                'Tokens imported'}: ${currentUserTokens.length}`}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default TokenChangeModal
