import { TSelectOption } from 'components/Select'

export interface IProject {
  avatar: string
  companyName: string
  customerAssistant: number
  description: string
  email: string
  end: Date
  fullAddress: string
  id: number
  manager: string | number
  methodology: string | number
  model: string | number
  name: string
  paymentMethod: string | number
  registerNumber: string
  stack: string
  stage: string | number
  start: Date
  structure: string | number
  unit: string | number
}

export interface IProfileProjectsProps {
  projects: IProject[]
  managers: TSelectOption[]
  loading: boolean
}
