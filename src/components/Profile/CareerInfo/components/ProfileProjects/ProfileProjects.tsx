import React, { useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import cls from 'classnames'

import { IProfileProjectsProps, IProject } from './ProfileProjects.model'
import styles from './ProfileProjects.module.scss'
import Spinner from 'components/Spinner'
import history from '../../../../../utils/history'
import { isAdmin as adminRole, isSuperAdmin as superAdminRole } from 'utils/user'

const ProfileProjects = ({ projects, managers, loading }: IProfileProjectsProps) => {
  const role = useSelector((state: TState) => state.auth.data.role)
  const [newProjects, setNewProjects] = useState<IProject[]>([])
  const words = useSelector((state: TState) => state.global.language.words)
  const adminSide = useMemo(() => adminRole(role) || superAdminRole(role), [role])

  useEffect(() => {
    if (projects) {
      let updatedProjects: IProject[] = []
      updatedProjects = projects.map((project: IProject) => {
        const manager = managers.find((option: any) => +option.value === project.manager)
        if (manager) {
          return { ...project, manager: manager.label }
        }
        return project
      })
      setNewProjects(updatedProjects)
    }
  }, [projects])

  const redirectToProject = (projectId: number) => {
    if (adminSide) {
      history.push(`/dashboard/projects/${projectId}`)
    }
  }

  return (
    <div className={styles['wrapper']}>
      {loading ? (
        <div className={styles['spinner']}>
          <Spinner />
        </div>
      ) : newProjects.length ? (
        <ul
          className={cls({
            [styles['project-list']]: true,
            [styles['project-list--user-side']]: !adminSide
          })}
        >
          {newProjects.map((project: IProject) => {
            return (
              <li key={`project_${project.id}`} onClick={() => redirectToProject(project.id)}>
                <div className={styles['project-wrapper']}>
                  <div className={styles['project-image']}>
                    <img src={project.avatar} alt={project.name} />
                  </div>
                  <div className={styles['project-name']}>{project.name}</div>
                </div>
                <div className={styles['project-manager']}>
                  <span>{words['projects.manager']}: </span>
                  {project.manager}
                </div>
              </li>
            )
          })}
        </ul>
      ) : (
        <div className={styles['empty-projects']}>{words['project.emptyList']}</div>
      )}
    </div>
  )
}

export default ProfileProjects
