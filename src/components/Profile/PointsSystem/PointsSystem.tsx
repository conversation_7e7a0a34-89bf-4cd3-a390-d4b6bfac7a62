import React, { FC, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import queryString from 'query-string'
import { HistoryTransaction } from 'screens/client/Profile/components/HistoryTransaction'
import styles from './PointsSystem.module.scss'
import { IToggleBarData, ToggleBar } from 'components/ToggleBar'
import { getPSType } from 'utils/toggleBarsValue'
import { useQueryParams } from 'hooks/useQueryParams'

type PointsSystemProps = {
  transactionToggleBarOptions: IToggleBarData[]
  id: string
  balance: number
  history: { push: any; location: { search: string; pathname: string } }
}

export const PointsSystem: FC<PointsSystemProps> = ({
  transactionToggleBarOptions,
  id,
  balance,
  history
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const parsed = useQueryParams()
  const toggleBarValue = useMemo(() => transactionToggleBarOptions.map(({ value }) => value), [
    transactionToggleBarOptions
  ])
  const selectedToggleBar = useMemo(
    () => (toggleBarValue.includes(parsed.type) ? parsed.type : toggleBarValue[0] || ''),
    [toggleBarValue, parsed.type]
  )

  return (
    <div>
      <span className={styles['mini-title']}>{words['admin.settings.transactionStory']}</span>
      <div className={styles.toggle}>
        <ToggleBar
          name="active-achievements-no-active"
          data={transactionToggleBarOptions}
          checked={selectedToggleBar}
          style={2}
          onChange={(value: IToggleBarData['value']) => {
            history.push(
              `${history.location.pathname}?${queryString.stringify({
                type: getPSType(value),
                profileType: parsed.profileType,
                userId: id
              })}`
            )
          }}
        />

        <div className={styles['points-balance']}>
          {words['user.header.balance']}: <span>{balance}</span>
        </div>
      </div>
      <div>
        <HistoryTransaction />
      </div>
    </div>
  )
}
