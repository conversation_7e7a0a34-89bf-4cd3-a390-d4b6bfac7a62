import React, { useEffect } from 'react'
import styles from './VerificationBlockPass.module.scss'
import { useSelector } from 'react-redux'
import { TState } from '../../../store'
import { Icons } from '../../Icons'
import { But<PERSON> } from 'components/UiKit/Button'
import { config, EVerificationBlockpass } from 'globalConfigs'

interface IProps {
  status: EVerificationBlockpass
  refId: number | string
  updateVerificationData: () => void
}

export const VerificationBlockPass = ({ status, refId, updateVerificationData }: IProps) => {
  const words = useSelector((state: TState) => state.global.language.words)

  useEffect(() => {
    loadBlockpassWidget()
  }, [status])

  const isActualStatus = Object.values(EVerificationBlockpass).includes(status)

  const isButtonVisible = [
    EVerificationBlockpass.NOT_EXIST,
    EVerificationBlockpass.REVIEW_REQUESTED,
    EVerificationBlockpass.REJECTED
  ].includes(status)

  const loadBlockpassWidget = () => {
    if (isButtonVisible) {
      const blockpass = new window.BlockpassKYCConnect(config.blockPassClientID, { refId })

      blockpass.startKYCConnect()
      blockpass.on('KYCConnectCancel', () => {
        updateVerificationData()
      })

      blockpass.on('KYCConnectClose', () => {
        updateVerificationData()
      })

      blockpass.on('KYCConnectSuccess', () => {
        // Not needed because it takes data via Blockpass API on the backend side
      })
    }
  }

  const statusMessage = (): { status: EVerificationBlockpass; icon: any } => {
    const text = isActualStatus ? `verification.status.${status}` : 'verification.status.not_exist'

    switch (status) {
      case EVerificationBlockpass.IN_COMPLETE:
        return { status: words[text], icon: <Icons icon="waiting" /> }
      case EVerificationBlockpass.WAITING:
        return { status: words[text], icon: <Icons icon="waiting" /> }
      case EVerificationBlockpass.IN_REVIEW:
        return { status: words[text], icon: <Icons icon="inreview" /> }
      case EVerificationBlockpass.REVIEW_REQUESTED:
        return { status: words[text], icon: <Icons icon="reviewRequested" /> }
      case EVerificationBlockpass.REJECTED:
        return { status: words[text], icon: <Icons icon="rejected" /> }
      case EVerificationBlockpass.BLOCKED:
        return { status: words[text], icon: <Icons icon="rejected" /> }
      case EVerificationBlockpass.APPROVED:
        return { status: words[text], icon: <Icons icon="approved" /> }
      default:
        return {
          status: words[text],
          icon: null
        }
    }
  }

  if (!status) {
    return <div />
  }

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.status}>
          <div className={styles.textStatus}>
            {`${words['user.profile.verificationStatus']} ${status && statusMessage().status}`}
          </div>

          {isActualStatus && (
            <div className={styles.message}>{words[`verification.message.${status}`]}</div>
          )}

          <div className={styles.imageStatus}>
            {statusMessage().icon}

            {isButtonVisible && (
              <Button id="blockpass-kyc-connect">{words['user.profile.verification']}</Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
