import React, { FC, startTransition, useMemo, useState } from 'react'
import cls from 'classnames'
import { TState } from 'store'
import { useSelector } from 'react-redux'
import { useHistory } from 'react-router'

import { ReactComponent as Facebook<PERSON>ogo } from 'assets/images/facebook.svg'
import { ReactComponent as InstagramLogo } from 'assets/images/instagram.svg'
import { ReactComponent as LinkedinLogo } from 'assets/images/linkedin.svg'
import { ReactComponent as LinkedinLogoDisable } from 'assets/images/linkedinDisable.svg'
import { ReactComponent as FacebookLogoDisable } from 'assets/images/facebookDisable.svg'
import { ReactComponent as InstagramLogoDisable } from 'assets/images/instagramDisable.svg'
import { ReactComponent as EmailLogo } from 'assets/images/email.svg'
import { ReactComponent as LocationLogo } from 'assets/images/location.svg'
import { ReactComponent as PhoneLogo } from 'assets/images/phone.svg'
import { ReactComponent as DefaultAvatar } from 'assets/images/default_avatar.svg'
import { ReactComponent as Citizenship } from 'assets/images/citizenship.svg'
import { ReactComponent as Gender } from 'assets/images/gender.svg'
import styles from './UserHeader.module.scss'
import { TUserHeaderProps } from './UserHeader.model'
import { EditBtnMenu } from 'screens/client/Profile/components/EditBtnMenu'
import { TerminationInformationModal } from './TerminationInformationModal'
import { InfoField } from './components/InfoField/InfoField'
import { SkeletonMui } from 'components/Skeleton'
import { Button } from 'components/UiKit/Button'
import { isAdmin as adminRole } from 'utils/user'
import { EProfileHistory } from 'globalConfigs'
import { useCountryName } from 'hooks/useCountryName'

export const UserHeader: FC<TUserHeaderProps | any> = ({
  isSameUser = false,
  isUserside,
  role,
  fullName,
  city,
  photo,
  rank,
  position,
  email,
  id,
  phoneNumber,
  socialNetworks,
  deactivated,
  terminationDate,
  deactivationComment,
  terminationInitiator,
  blackList,
  countNegativeReviews,
  countPositiveReviews,
  citizenship,
  gender,
  countryOfResidence,
  isContractor
}: any) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const [open, setOpen] = useState(false)

  const history = useHistory()
  const getCountryName = useCountryName()

  const showHiddenInfo = isSameUser || !isUserside

  const isAdmin = useMemo(() => adminRole(role), [role])

  const toggleTerminationInformationModal = (): void => {
    setOpen(!open)
  }

  const userSkils =
    rank && rank.name && position && position.name
      ? `${words[rank.name]} / ${words[position.name]}`
      : ''

  const redirectToEditProfile = () => {
    startTransition(() => {
      history.push(
        `/dashboard/profile/editProfile?profileType=${EProfileHistory.MAIN_INFO}&userId=${id}`
      )
    })
  }

  const userPhoto = photo ? <img src={photo} alt="User photo" /> : <DefaultAvatar />

  return (
    <div
      className={cls({
        [styles.main]: true,
        [styles['deactivated-profile']]: deactivated
      })}
    >
      <div className={styles.information}>
        <div className={styles['personal-info']}>
          {!!fullName ? (
            userPhoto
          ) : (
            <SkeletonMui variant="circular" className={styles['img-placeholder']} />
          )}
          {!isContractor && (
            <div className={styles.socials}>
              <a href={socialNetworks && socialNetworks.linkedin} target="blank">
                {socialNetworks && socialNetworks.linkedin ? (
                  <LinkedinLogo />
                ) : (
                  <LinkedinLogoDisable />
                )}
              </a>
              <a href={socialNetworks && socialNetworks.facebook} target="blank">
                {socialNetworks && socialNetworks.facebook ? (
                  <FacebookLogo />
                ) : (
                  <FacebookLogoDisable />
                )}
              </a>
              <a href={socialNetworks && socialNetworks.instagram} target="blank">
                {socialNetworks && socialNetworks.instagram ? (
                  <InstagramLogo />
                ) : (
                  <InstagramLogoDisable />
                )}
              </a>
            </div>
          )}
        </div>

        <div className={styles['description-info']}>
          <div className={styles['description-name']}>{fullName || <SkeletonMui />}</div>
          <div className={styles['description-position']}>{userSkils || <SkeletonMui />}</div>
          <div className={styles['description-rating-container']}>
            <div className={styles['rating-label']}>{words['user.profile.buttons.rating']}:</div>
            <div>
              <span className={styles['rating-value-positive']}>+{countPositiveReviews || 0}</span>
              <span className={styles['rating-value-negative']}>-{countNegativeReviews || 0}</span>
            </div>
          </div>
        </div>
      </div>

      <div className={styles['main-info']}>
        <InfoField info={city}>
          <LocationLogo className={styles['main-info-logo']} />
          <span className={styles['main-info-text']}>{`${getCountryName(
            countryOfResidence
          )}, ${city}`}</span>
        </InfoField>
        {!isContractor && (
          <InfoField info={email}>
            <EmailLogo className={styles['main-info-logo']} />
            <span className={styles['main-info-text']}>{email}</span>
          </InfoField>
        )}
        {!isContractor && (
          <InfoField info={phoneNumber}>
            <PhoneLogo className={styles['main-info-logo']} />
            <span className={styles['main-info-text']}>{phoneNumber}</span>
          </InfoField>
        )}
        <InfoField info={citizenship}>
          <Citizenship className={styles['main-info-logo']} />
          <span className={styles['main-info-text']}>{getCountryName(citizenship)}</span>
        </InfoField>
        <InfoField info={gender}>
          <Gender className={styles['main-info-logo']} />
          <span className={styles['main-info-text']}>{gender}</span>
        </InfoField>
      </div>

      <div className={styles['mood-scale-container']}>
        {isAdmin && (
          <EditBtnMenu
            userId={id}
            deactivated={deactivated}
            redirectToEditProfile={redirectToEditProfile}
          />
        )}
        {!isAdmin && showHiddenInfo && (
          <Button className={styles['edit-btn']} onClick={redirectToEditProfile}>
            {words['user.profile.editBtn']}
          </Button>
        )}

        {!!fullName && deactivated && (
          <div className={styles.deactivated}>
            <div className={styles['deactivated-row']}>
              <span>{words['admin.team.deactivated']}</span>
              <div onClick={toggleTerminationInformationModal} className={styles['question-icon']}>
                ?
              </div>
            </div>
          </div>
        )}
      </div>

      <TerminationInformationModal
        toggleTerminationInformationModal={toggleTerminationInformationModal}
        open={open}
        terminationDate={terminationDate}
        deactivationComment={deactivationComment}
        terminationInitiator={terminationInitiator}
        blackList={blackList}
      />
    </div>
  )
}
