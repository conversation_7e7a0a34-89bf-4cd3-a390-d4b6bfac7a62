import { TSelectOption } from 'components/Select'

export type TUserHeaderProps = {
  role: string
  fullName: string
  city: string
  photo: string
  rank: TSelectOption
  position: TSelectOption
  email: string
  id: number
  phoneNumber: string
  roles: string[]
  socialNetworks: {
    facebook: string
    linkedin: string
    instagram: string
  }
  deactivated: string
  terminationDate: string
  deactivationComment: string
  terminationInitiator: { id: number; name: string } | null
  blackList: boolean
  isUserside: boolean
  countryOfResidence: string
  isContractor?: boolean
}
