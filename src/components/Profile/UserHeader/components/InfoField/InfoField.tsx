import React from 'react'
import styles from '../../UserHeader.module.scss'
import { SkeletonMui } from 'components/Skeleton'

type TProperty = {
  info: string
  children: React.ReactNode
}

const InfoField: React.FC<TProperty> = ({ info, children }) => (
  <div className={styles['main-info-item']}>
    {info ? children : <SkeletonMui className={styles['text-placeholder']} />}
  </div>
)

export { InfoField }
