import React, { FC, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import cls from 'classnames'
import Spinner from 'components/Spinner'
import { SkeletonMui } from 'components/Skeleton'
import { Select } from 'Select/Select'
import { convertIdNameToSelectOptions, ISelect } from 'utils/convertIdNameToSelectOptions'
import {
  IBank,
  IBankAccountWithDetails,
  ICurrency,
  IEntrDataWithService
} from 'models/admin-settings-models'

import styles from './EntrepreneurInfo.module.scss'

type TOption = { label: string; value: string }

interface IEntrepreneurInfoProps {
  loading: boolean
  banks: IBank[]
  currencies: ICurrency[]
  entrepreneur: IEntrDataWithService | null
  bankAccounts: IBankAccountWithDetails[]
}

export const EntrepreneurInfo: FC<IEntrepreneurInfoProps> = ({
  loading,
  banks,
  currencies,
  entrepreneur,
  bankAccounts
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const [bankValue, setBankValue] = useState<TOption>({ value: '@ALL@', label: words['all'] })
  const [currencyValue, setCurrencyValue] = useState<TOption>({
    value: '@ALL@',
    label: words['all']
  })

  const bankOptions: ISelect[] = useMemo(() => {
    const options = convertIdNameToSelectOptions(banks)
    return [{ value: '@ALL@', label: words['all'] }, ...options]
  }, [banks])
  const currencyOptions: ISelect[] = useMemo(() => {
    const options = convertIdNameToSelectOptions(currencies)
    return [{ value: '@ALL@', label: words['all'] }, ...options]
  }, [currencies])

  const filteredBankAccounts = useMemo(
    () =>
      bankAccounts
        .filter(account => bankValue.value === '@ALL@' || account.bank.id === +bankValue.value)
        .filter(
          account => currencyValue.value === '@ALL@' || account.currency.id === +currencyValue.value
        ),
    [bankAccounts, bankValue, currencyValue]
  )

  const getEntrepreneurData = (key: keyof Omit<IEntrDataWithService, 'service'>) =>
    entrepreneur ? entrepreneur[key] || null : null

  const entrepreneurData = useMemo(() => {
    const isPrivateEntre = entrepreneur ? entrepreneur.isPrivate : null

    const translatedNameField = {
      subtitle: words['user.profile.entrepreneur.translatedFullName'] || 'Translated Full Name',
      value: getEntrepreneurData('translatedFullName')
    }
    const translatedAddressField = {
      subtitle: words['user.profile.entrepreneur.translatedAddress'] || 'Translated Address',
      value: getEntrepreneurData('translatedAddress')
    }
    const translatedUsrAddressField = {
      subtitle:
        words['user.profile.entrepreneur.translatedUsrAddress'] ||
        'Translated USR registration address',
      value: getEntrepreneurData('translatedUsrAddress')
    }
    const entrTypeField = {
      subtitle: words['user.profile.entrepreneur.type'] || 'Entrepreneur type',
      value: isPrivateEntre
        ? words['user.profile.entrepreneur.type.private'] || 'Private Entrepreneur'
        : words['user.profile.entrepreneur.type.individual'] || 'Individual (private person)'
    }

    return [
      {
        subtitle: words['user.profile.entrepreneur.fullName'] || 'Full Name',
        value: getEntrepreneurData('fullName')
      },
      ...(translatedNameField.value ? [translatedNameField] : []),
      {
        subtitle: words['user.profile.entrepreneur.address'] || 'Address',
        value: getEntrepreneurData('address')
      },
      ...(translatedAddressField.value ? [translatedAddressField] : []),
      {
        subtitle: words['user.profile.entrepreneur.itn'] || 'ITN',
        value: getEntrepreneurData('itn')
      },
      ...(isPrivateEntre !== null ? [entrTypeField] : []),
      ...(isPrivateEntre
        ? [
            {
              subtitle: words['user.profile.entrepreneur.usrNumber'] || 'USR number',
              value: getEntrepreneurData('usrNumber')
            },
            {
              subtitle: words['user.profile.entrepreneur.usrDate'] || 'USR registration date',
              value: getEntrepreneurData('usrDate')
            },
            {
              subtitle: words['user.profile.entrepreneur.usrAddress'] || 'USR registration address',
              value: getEntrepreneurData('usrAddress')
            },
            ...(translatedUsrAddressField.value ? [translatedUsrAddressField] : [])
          ]
        : []),
      {
        subtitle: words['user.profile.entrepreneur.service'] || 'Entrepreneur service',
        value: entrepreneur ? entrepreneur.service.name : null
      }
    ]
  }, [entrepreneur])

  return (
    <div className={styles['wrapper']}>
      <div className={cls([styles['block'], styles['entrepreneur-block']])}>
        <h2>{words['user.profile.entrepreneur.info'] || 'Entrepreneur Data'}</h2>
        <div className={styles['entrepreneur-container']}>
          {entrepreneurData.map(({ subtitle, value }, idx) => (
            <div key={idx} className={styles['content-block']}>
              <p className={styles['subtitle']}>{subtitle}:</p>
              {value ? <p className={styles['value']}>{value}</p> : <SkeletonMui />}
            </div>
          ))}
        </div>
      </div>
      <div className={cls([styles['block'], styles['bank-accounts-block']])}>
        <h2>{words['user.profile.bankAccount.info'] || 'Bank Accounts Data'}</h2>
        <div className={styles['bank-accounts-container']}>
          <div className={styles['filter-container']}>
            <div className={styles['filter-block']}>
              <p>{words['admin.entrepreneurs.bank'] || 'Bank'}</p>
              <Select
                className={styles['select']}
                options={bankOptions}
                value={bankValue}
                onChange={value => setBankValue(value as TOption)}
              />
            </div>
            <div className={styles['filter-block']}>
              <p>{words['admin.entrepreneurs.currency'] || 'Currency'}</p>
              <Select
                className={styles['select']}
                options={currencyOptions}
                value={currencyValue}
                onChange={value => setCurrencyValue(value as TOption)}
              />
            </div>
          </div>
          {!!filteredBankAccounts.length ? (
            <div className={styles['table']}>
              <div className={styles['row']}>
                <div>{words['user.profile.bankAccount.bank'] || 'Bank'}</div>
                <div>{words['user.profile.bankAccount.currency'] || 'Currency'}</div>
                <div>{words['user.profile.bankAccount.iban'] || 'Iban'}</div>
                <div>{words['user.profile.bankAccount.type'] || 'Type'}</div>
                <div>{words['user.profile.bankAccount.translatedType'] || 'Translated Type'}</div>
                <div>{words['user.profile.bankAccount.routingNumber'] || 'Routing number'}</div>
              </div>
              {filteredBankAccounts.map(account => (
                <div key={account.id} className={styles['row']}>
                  <div>{account.bank.name}</div>
                  <div>{account.currency.name}</div>
                  <div>{account.iban}</div>
                  {account.type ? <div>{account.type}</div> : <SkeletonMui />}
                  {account.translatedType ? <div>{account.translatedType}</div> : <SkeletonMui />}
                  {account.routingNumber ? <div>{account.routingNumber}</div> : <SkeletonMui />}
                </div>
              ))}
            </div>
          ) : (
            <div className={styles['empty-message']}>
              {words['user.profile.bankAccount.empty'] || 'This user doesn`t have a bank account'}
            </div>
          )}
        </div>
      </div>
      {loading && <Spinner isBackground />}
    </div>
  )
}
