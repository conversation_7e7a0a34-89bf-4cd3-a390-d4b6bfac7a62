@import 'assets/style/colors';
@import 'assets/style/variables';
@import 'assets/style/mixins';

.wrapper {
  display: flex;
  gap: 24px;
  min-height: 320px;
}

.block {
  @include profile-info-section;

  margin: 0;
  font-size: 14px;
  line-height: 24px;
}

.entrepreneur-block {
  width: 360px;
}

.bank-accounts-block {
  flex: 1;
}

.content-block {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .value {
    margin: 0;
    align-self: flex-end;
    text-align: left;
  }
}

.subtitle {
  margin: 0;
  color: $blue;
  font-weight: 700;
  text-align: left;
}

.entrepreneur-container,
.bank-accounts-container {
  margin-top: 20px;
}

.entrepreneur-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bank-accounts-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-container {
  display: flex;
  gap: 20px;
}

.filter-block {
  display: flex;
  flex-direction: column;
  gap: 4px;

  p {
    color: $grey;
    margin: 0;
    text-align: left;
    font-size: 12px;
    line-height: 16px;
  }

  .select {
    width: 172px;
    margin: 0;
  }
}

.table {
  border: 1px solid $light-grey;
  border-radius: 12px;
  overflow: clip;
}

.row {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid $light-grey;

  & > * {
    flex: 1;
  }
}

.empty-message {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
