/* eslint-disable react/jsx-no-target-blank */
import React, { FC, ReactElement } from 'react'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import cn from 'classnames'

import { Icons } from '../../../Icons'
import { ReactComponent as FileLogo } from 'assets/images/logo-file.svg'
import styles from './FileView.module.scss'
import { useSelector } from 'react-redux'
import { TState } from 'store'

interface FileViewProps {
  isEdit: boolean
  item: {
    name: string
    link: string
  }
  idx: number
  deleteFile: (item: any, idx: number, choosen: any, setChoosen: any) => void
  choosen?: any
  setChoosen?: any
}

export const FileView: FC<FileViewProps> = ({
  isEdit,
  item,
  idx,
  deleteFile,
  choosen,
  setChoosen
}): ReactElement => {
  const words = useSelector((state: TState) => state.global.language.words)
  return (
    <div className={styles['file-container']} id="discharge">
      <a className={styles['file-name-link']} href={item.link} target="_blank">
        <FileLogo />
        <div className={styles['file-name']}>{item.name}</div>
      </a>
      {isEdit && (
        <div
          className={styles['del-file-icon']}
          onClick={() => deleteFile(item, idx, choosen, setChoosen)}
          data-tip="true"
          data-for="discharge"
        >
          <Icons icon="dischargeOutline" />
          <ReactTooltip
            className={cn(styles.tooltip, styles['tooltip-discharge'])}
            id="discharge"
            anchorSelect="#discharge"
            place="bottom"
          >
            {words['admin.users.table.buttons.delete']}
          </ReactTooltip>
        </div>
      )}
    </div>
  )
}
