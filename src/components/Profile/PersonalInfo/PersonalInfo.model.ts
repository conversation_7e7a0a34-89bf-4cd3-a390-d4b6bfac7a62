import { TSelectOption } from 'components/Select'

export type TPersonalInfoProps = {
  hideForbiddenInfo?: boolean
  dateOfBirth: any
  hometown: string
  maritalStatus: TSelectOption
  amountOfChildren: string
  university: string
  degree: TSelectOption
  hobby: string
  lifeGoals: string
  lifePriorities: TSelectOption
  testResult: string
  userfiles: any
}

export type TLifePrioritiesProps = {
  priorities: any
}

export type TChildrenBirthDayProps = {
  childrenArr: any
}
