@import '../Resume.module.scss';

// about component grid
.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $space-xl;
  margin: $space-m 0;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    display: block;
    height: 216px;
    width: 1px;
    background: #EBEBEB;
    left: 50%;
    top: 50%;
    transform: translateY(-50%);
  }
}

@each $side in left, right {
  .about-#{$side} {
    display: flex;
    flex-direction: column;
    gap: $space-m;
    text-align: start;
  }
}

.experience-label {
  font-weight: 600;
  color: $blue;
  padding-right: 5px;
  white-space: nowrap;
}

.experience-value {
  width: 100%;
}

.experience-value-skeleton,
.experience-value-skeleton--bg {
  width: 100%;
  background: rgba(0, 0, 0, 0.11);
  border-radius: 4px;
}

.experience-value-skeleton {
  height: 20px;
}

.experience-value-skeleton--bg {
  height: 60px;
}

.about-label {
  font-weight: 600;
  color: $blue;
  margin-bottom: $space-s;
}


.about-experience-block,
.about-text-block,
.about-technologies-block,
.about-languages-block {
  margin-bottom: $space-m;
}

.about-experience-block {
  display: flex;
  align-items: center;
}

.about-text,
.about-technologies-text {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.about-text {
  text-align: justify;
}

.about-technologies-text {
  text-align: left;
}

.about-languages-list {
  margin-top: $space-s;
}

.about-language-item {
  display: flex;
  margin-bottom: $space-s;
  font-size: 14px;
}

.about-language-level {
  font-weight: 600;
  margin-left: 5px;
}

// Education section
@include section-list(education);

.education-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.education-item-institution {
  font-size: 16px;
  font-weight: 600;
  text-align: start;
  color: $dark-grey;
  line-height: 1.5;
  width: 70%;
  padding-right: 5px;
}

.education-item-meta {
  width: 30%;
}

.education-item-degree {
  font-size: 14px;
  text-align: start;
  color: $dark-grey;
  line-height: 1.5;
}

.education-item-period {
  margin-top: 4px;
}

// Experience section
@include section-list(exp);

.exp-list {
  gap: $space-l;
}
.exp-item-header,
.education-item-header,
.project-item-header {
  display: flex;
  justify-content: space-between;
}

.exp-item-header-right,
.project-item-header-right {
  max-width: 30%;
}
.exp-item-header-left,
.project-item-header-left {
  max-width: 70%;
  padding-right: 5px;
}

.exp-item-period,
.exp-item-location {
  overflow: hidden;
  text-align: right;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  white-space: unset;
}

.exp-item-period {
  margin-bottom: 5px;
}

.exp-item-position {
  font-size: 16px;
  font-weight: 600;
  text-align: start;
  color: $dark-grey;
  margin-bottom: 4px;
}

.exp-item-company,
.project-item-position-wrapper {
  display: flex;
  align-items: center;
  font-size: 14px;
  text-align: start;
  color: $dark-grey;
  margin-bottom: $space-s;

  div:nth-of-type(2) {
    margin: 0 5px;
  }
}

.exp-item-company-name {
  max-width: 70%;
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
}

.exp-item-description {
  font-size: 14px;
  line-height: 1.5;
  color: $dark-grey;
  text-align: justify;
  margin-top: 16px;

  span:first-child {
    font-weight: 600;
    margin-right: 5px
  }
}

// Projects section
@include section-list(projects);

.projects-item-name {
  font-size: 16px;
  font-weight: 600;
  text-align: start;
  color: $dark-grey;
  margin-bottom: 10px;

  a {
    color: $dark-grey;
  }
}

.project-item-position {
  font-size: 14px;
  color: $dark-grey;
}

.projects-item-description,
.projects-item-tech {
  font-size: 14px;
  line-height: 1.5;
  color: $dark-grey;
  text-align: justify;
  
  span:first-of-type {
    padding-right: 5px;
    font-weight: 600;
  }
}

.projects-item-description {
  margin-bottom: 12px;
}