import React, { FC, Fragment } from 'react'
import moment from 'moment'

import styles from './ResumeComponents.module.scss'
import { ReactComponent as WorkIcon } from '../../../../assets/images/work.svg'
import { IUserExperience } from 'screens/client/EditProfile/EditProfile.model'
import { useCountries } from 'hooks/useCountries'
import { getCoumtryName } from 'helpers/helpers'

interface IResumeExperienceProps {
  words: any
  data: IUserExperience[]
}

export const ResumeExperience: FC<IResumeExperienceProps> = ({ words, data }) => {
  const { countryOptions } = useCountries()

  return (
    <div className={styles['exp-wrapper']}>
      <h3 className={styles['exp-title']}>
        {words['user.profile.resume.experience'] || 'Experience'}
      </h3>

      <div className={styles['exp-list']}>
        {data.map((exp: IUserExperience, idx: number) => (
          <Fragment key={`Experience${idx}`}>
            <div className={styles['exp-item']}>
              <div className={styles['exp-item-icon']}>
                <WorkIcon />
              </div>

              <div className={styles['exp-item-content']}>
                <div className={styles['exp-item-header']}>
                  <div className={styles['exp-item-header-left']}>
                    <div className={styles['exp-item-position']}>{words[exp.position.name]}</div>
                    <div className={styles['exp-item-company']}>
                      <div className={styles['exp-item-company-name']}>
                        {exp.link ? (
                          <a href={exp.link} target="_blank" rel="noopener noreferrer">
                            {exp.company}
                          </a>
                        ) : (
                          exp.company
                        )}
                      </div>
                      <div>•</div>
                      <div>{words[exp.typeOfEmployment.name]}</div>
                    </div>
                  </div>

                  <div className={styles['exp-item-header-right']}>
                    <div className={styles['exp-item-period']}>
                      {`${moment(exp.startDate).format('MMMM YYYY')} - ${
                        exp.isActive
                          ? words['user.profile.resume.presentTime'] || 'Present time'
                          : moment(exp.endDate).format('MMMM YYYY')
                      }`}
                    </div>
                    {(exp.city || exp.country) && (
                      <div className={styles['exp-item-location']}>
                        {exp.city && exp.country
                          ? `${exp.city}, ${getCoumtryName(exp.country, countryOptions)}`
                          : exp.city || getCoumtryName(exp.country, countryOptions)}
                      </div>
                    )}
                  </div>
                </div>

                {exp.responsibilities && (
                  <div className={styles['exp-item-description']}>
                    <span>
                      {words['user.profile.resume.responsibilities'] || 'Responsibilities:'}
                    </span>
                    <span>{exp.responsibilities}</span>
                  </div>
                )}
              </div>
            </div>
            {idx < data.length - 1 && <div className={styles['exp-exp-divider']} />}
          </Fragment>
        ))}
      </div>
    </div>
  )
}
