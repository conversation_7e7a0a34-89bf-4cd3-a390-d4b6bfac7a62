import React, { FC, Fragment, useMemo } from 'react'
import styles from './ResumeComponents.module.scss'
import { ILanguage, IResume, ISelectOptions } from 'screens/client/EditProfile/EditProfile.model'

interface IResumeAboutProps {
  words: any
  data: IResume
}

export const ResumeAbout: FC<IResumeAboutProps> = ({ words, data }) => {
  const { technologies, languages } = useMemo(() => {
    const techs: any = []
    const langs: any = []

    if (data.technologiesUsers) {
      data.technologiesUsers.forEach((tech: { technologies: ISelectOptions }) =>
        techs.push(tech.technologies.name)
      )
    }

    if (data.languagesUsers) {
      data.languagesUsers.forEach((lang: ILanguage) => {
        langs.push({ label: lang.languages.name, level: lang.level })
      })
    }

    return { technologies: techs, languages: langs }
  }, [data])

  return (
    <div className={styles['about-wrapper']}>
      <h2 className={styles['about-title']}>{words['user.profile.togglebarresume'] || 'Resume'}</h2>
      <div className={styles['about-grid']}>
        <div className={styles['about-left']}>
          <div className={styles['about-experience-block']}>
            {data.experience ? (
              <>
                <div className={styles['experience-label']}>
                  {words['user.profile.resume.experience'] || 'Years of Experience:'}
                </div>

                <div className={styles['experience-value']}>{words[`${data.experience.name}`]}</div>
              </>
            ) : (
              <div className={styles['experience-value-skeleton']} />
            )}
          </div>

          {/* about self text block */}
          <div className={styles['about-text-block']}>
            {data.aboutMe ? (
              <>
                <div className={styles['about-label']}>
                  {words['user.profile.resume.aboutMe'] || 'About Me:'}
                </div>
                <p className={styles['about-text']}>{data.aboutMe}</p>
              </>
            ) : (
              <div className={styles['experience-value-skeleton--bg']} />
            )}
          </div>
        </div>
        <div className={styles['about-right']}>
          {/* technologies block */}
          <div className={styles['about-technologies-block']}>
            {technologies.length ? (
              <>
                <div className={styles['about-label']}>
                  {words['user.profile.resume.technologies'] || 'Technologies:'}
                </div>
                <div className={styles['about-technologies-text']}>
                  {technologies.map((tech: string, index: number) => (
                    <Fragment key={`technology${index}`}>
                      {tech}
                      {index < technologies.length - 1 && ', '}
                    </Fragment>
                  ))}
                </div>
              </>
            ) : (
              <div className={styles['experience-value-skeleton--bg']} />
            )}
          </div>

          {/* languages block */}
          <div className={styles['about-languages-block']}>
            {languages.length ? (
              <>
                <div className={styles['about-label']}>
                  {words['user.profile.resume.languages'] || 'Languages:'}
                </div>
                <div className={styles['about-languages-list']}>
                  {languages.map((language: { label: string; level: string }, i: number) => (
                    <div className={styles['about-language-item']} key={`Lang${i}`}>
                      <span>
                        {words[language.label]}:
                        <span className={styles['about-language-level']}>{language.level}</span>
                      </span>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className={styles['experience-value-skeleton--bg']} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
