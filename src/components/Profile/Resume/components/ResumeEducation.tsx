import React, { FC, Fragment } from 'react'
import moment from 'moment'

import styles from './ResumeComponents.module.scss'
import { ReactComponent as EducationIcon } from '../../../../assets/images/education.svg'
import { IUserEducation } from 'screens/client/EditProfile/EditProfile.model'

interface IResumeEducationProps {
  words: any
  data: IUserEducation[]
}

export const ResumeEducation: FC<IResumeEducationProps> = ({ words, data }) => (
  <div className={styles['education-wrapper']}>
    <h3 className={styles['education-title']}>{words['user.profile.education'] || 'Education'}</h3>

    <div className={styles['education-list']}>
      {data.map((education: IUserEducation, i: number) => (
        <Fragment key={`education${i}`}>
          <div className={styles['education-item']}>
            <div className={styles['education-item-icon']}>
              <EducationIcon />
            </div>
            <div className={styles['education-item-content']}>
              <div className={styles['education-item-header']}>
                <div className={styles['education-item-institution']}>{education.university}</div>
                <div className={styles['education-item-meta']}>
                  <div className={styles['education-item-period']}>
                    {`${moment(education.startDate).format('MMMM YYYY')} - ${moment(
                      education.endDate
                    ).format('MMMM YYYY')}`}
                  </div>
                </div>
              </div>

              <div className={styles['education-item-degree']}>
                {`${education.specialization} • ${words[education.academicDegree.name]}`}
              </div>
            </div>
          </div>
          {i < data.length - 1 && <div className={styles['education-divider']} />}
        </Fragment>
      ))}
    </div>
  </div>
)
