import React, { FC, Fragment } from 'react'
import styles from './ResumeComponents.module.scss'
import { ReactComponent as ProjectsIcon } from '../../../../assets/images/projects.svg'
import { IUserProjects } from 'screens/client/EditProfile/EditProfile.model'
import moment from 'moment'
import { useCountries } from 'hooks/useCountries'
import { getCoumtryName } from 'helpers/helpers'

interface IResumeProjectsProps {
  words: any
  data: IUserProjects[]
}

export const ResumeProjects: FC<IResumeProjectsProps> = ({ words, data }) => {
  const { countryOptions } = useCountries()

  return (
    <div className={styles['projects-wrapper']}>
      <h3 className={styles['projects-title']}>
        {words['user.profile.resume.projects'] || 'Projects'}
      </h3>
      <div className={styles['projects-list']}>
        {data.map((project: IUserProjects, idx: number) => (
          <Fragment key={`project_${idx}`}>
            <div className={styles['projects-item']}>
              <div className={styles['projects-item-icon']}>
                <ProjectsIcon />
              </div>

              <div className={styles['projects-item-content']}>
                <div className={styles['project-item-header']}>
                  <div className={styles['project-item-header-left']}>
                    <div className={styles['projects-item-name']}>
                      {project.link ? (
                        <a href={project.link} target="_blank" rel="noopener noreferrer">
                          {project.name}
                        </a>
                      ) : (
                        project.name
                      )}
                    </div>
                    <div className={styles['project-item-position-wrapper']}>
                      <div className={styles['project-item-position']}>
                        {words[project.position.name]}
                      </div>
                      <div>•</div>
                      <div>{words[project.typeOfEmployment.name]}</div>
                    </div>
                  </div>

                  <div className={styles['project-item-header-right']}>
                    <div className={styles['exp-item-period']}>
                      {`${moment(project.startDate).format('MMMM YYYY')} - ${
                        project.isActive
                          ? words['user.profile.resume.presentTime'] || 'Present time'
                          : moment(project.endDate).format('MMMM YYYY')
                      }`}
                    </div>
                    {(project.city || project.country) && (
                      <div className={styles['exp-item-location']}>
                        {project.city && project.country
                          ? `${project.city}, ${getCoumtryName(project.country, countryOptions)}`
                          : project.city || getCoumtryName(project.country, countryOptions)}
                      </div>
                    )}
                  </div>
                </div>

                {project.description && (
                  <div className={styles['projects-item-description']}>
                    <span>
                      {words['user.profile.resume.projectDescription'] || 'Project Description:'}
                    </span>
                    <span>{project.description}</span>
                  </div>
                )}
                {project.technologiesUserProjects && (
                  <div className={styles['projects-item-tech']}>
                    <span>{words['user.profile.resume.technologies'] || 'Technologies:'}</span>
                    <span>
                      {project.technologiesUserProjects.map((tech: any, i: number) => {
                        return `${tech.technologies.name}${
                          i < project.technologiesUserProjects.length - 1 ? ', ' : ''
                        }`
                      })}
                    </span>
                  </div>
                )}
              </div>
            </div>
            {idx < data.length - 1 && <div className={styles['projects-divider']} />}
          </Fragment>
        ))}
      </div>
    </div>
  )
}
