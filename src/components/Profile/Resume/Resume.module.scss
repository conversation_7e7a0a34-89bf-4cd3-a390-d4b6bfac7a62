@import '../../../assets/style/colors';
@import "../../../assets/style/variables";
@import "../../../assets/style/mixins";

// base section wrapper
%section-wrapper {
  @include profile-info-section;
  margin-bottom: 24px;
}

// titles
%section-title {
  margin: 0;
  text-align: start;
  font-weight: 700;
  color: $dark-grey;
}

// common styles for list item parts
%item-icon {
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 1px solid $light-blue;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

%item-icon svg {
  width: 24px;
  height: 24px;
  display: block;
}

%item-content {
  flex: 1;
}

%item-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

%item-period {
  font-size: 12px;
  color: $grey;
  white-space: nowrap;
}

%divider {
  height: 1px;
  background: #EBEBEB;
  width: 100%;
}

// Spacing scale
$space-s: 8px;
$space-m: 16px;
$space-l: 24px;
$space-xl: 32px;

// Mixins

// section title mixin
@mixin section-title($size: 20px) {
  @extend %section-title;
  font-size: $size;
}

// list items layout
@mixin list-item-card($padding: $space-m 0) {
  display: flex;
  align-items: flex-start;
  gap: $space-m;
  padding: $padding;
}

// List items for sections
@mixin section-list($prefix, $has-dividers: true) {
  .#{$prefix}-list { 
    display: flex; 
    flex-direction: column; 
    margin-top: $space-m;
  }

  .#{$prefix}-item {
    @include list-item-card;
    
    &-icon { @extend %item-icon; }
    &-content { @extend %item-content; }
    &-meta { @extend %item-meta; }
    &-period { @extend %item-period; }
    &-location { @extend %item-period; }
  }

  @if $has-dividers {
    .#{$prefix}-divider {
      @extend %divider;
    }
  }
}

// section wrappers & titles
$sections: (
  about: (title-size: 24px),
  education: (),
  exp: (),
  projects: ()
);

@each $name, $opts in $sections {
  .#{$name}-wrapper {
    @extend %section-wrapper;
  }
  .#{$name}-title {
    @include section-title(map-get($opts, title-size) or 20px);
  }
}

.resume {
  gap: 24px;
}
