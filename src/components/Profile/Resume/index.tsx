import React, { FC } from 'react'

import styles from './Resume.module.scss'
import { ResumeAbout } from './components/ResumeAbout'
import { ResumeEducation } from './components/ResumeEducation'
import { ResumeExperience } from './components/ResumeExperience'
import { ResumeProjects } from './components/ResumeProjects'
import { IResume } from 'screens/client/EditProfile/EditProfile.model'

interface IResumeProps {
  words: any
  resumeData: IResume
}

export const Resume: FC<IResumeProps> = ({ words, resumeData }) => (
  <div className={styles.resume}>
    <ResumeAbout data={resumeData} words={words} />
    {resumeData && resumeData.userEducation && !!resumeData.userEducation.length && (
      <ResumeEducation data={resumeData.userEducation} words={words} />
    )}
    {resumeData && resumeData.userExperience && !!resumeData.userExperience.length && (
      <ResumeExperience data={resumeData.userExperience} words={words} />
    )}
    {resumeData && resumeData.userProjects && !!resumeData.userProjects.length && (
      <ResumeProjects data={resumeData.userProjects} words={words} />
    )}
  </div>
)
