import React, { FC } from 'react'
import cls from 'classnames'
import { CellTh } from './CellTh'
import { CellTd } from './CellTd'
import styles from './style.module.scss'
import '../../assets/style/pagination.scss'
import { ITableProps } from './model'
import Pagination from 'rc-pagination'
import { ReactComponent as Prev } from 'assets/images/prev.svg'
import { ReactComponent as Next } from 'assets/images/next.svg'
import { isUser } from 'utils/user'

export const Table: FC<ITableProps> = ({
  data,
  role = '4',
  columns,
  handleOnChangeSort,
  isSorted,
  nameIsSorted,
  activePage,
  handlePageChange,
  itemsCountPerPage,
  totalItemsCount,
  isColActionsCell,
  ActionsCellContent,
  actionsTableHeader,
  columnsWidthOption,
  tableDataHeightOption,
  isFooter,
  footerTitle,
  footerContent,
  isPagination,
  onRowClick,
  isTimeOffRequests = false
}) => {
  const addColActionsCell = (content: any) => <CellTd className="action">{content}</CellTd>

  const rowDataClass = cls(
    styles[`table-row-data-${columnsWidthOption}`],
    styles[`table-flex-container-${columnsWidthOption}`]
  )
  const headerStyle = cls({
    [styles[`table-row-header-${columnsWidthOption}`]]: true,
    [styles[`table-row-header-${columnsWidthOption}-not-user`]]: !isUser(role)
  })

  return (
    <div className={styles[`table-wrapper-${columnsWidthOption}`]}>
      {columns && (
        <div className={headerStyle}>
          {/*<div className={styles[`table-row-header-${columnsWidthOption}`]}>*/}
          <div className={styles[`table-flex-container-${columnsWidthOption}`]}>
            {columns.map((element: any, index: any) => (
              <CellTh
                role={role}
                key={index}
                className={columnsWidthOption}
                dataName={element.dataName}
                title={element.header}
                isSorting={element.isSorting}
                isSorted={isSorted}
                nameIsSorted={nameIsSorted}
                setOrderInTable={handleOnChangeSort}
              />
            ))}
            {isColActionsCell ? addColActionsCell('Действие') : null}
          </div>
          {actionsTableHeader}
        </div>
      )}
      <div className={styles[`${tableDataHeightOption}`]}>
        {data.length !== 0
          ? data
              .reduce((init: any, item: any) => {
                // TODO -- костыль пока не будет готова новая таблица
                const newItem = {
                  ...item
                }

                if (newItem.id) {
                  delete newItem.id
                }

                if (newItem.userId) {
                  delete newItem.userId
                }

                init.push(newItem)

                return init
              }, [])
              .map((element: any, index: any) => (
                <div
                  onClick={() => {
                    if (onRowClick && data[index].id) {
                      onRowClick(data[index].id)
                    }
                  }}
                  className={rowDataClass}
                  key={index}
                  data-index={index}
                >
                  {Object.values(element).map((item: any, i: number) => (
                    <CellTd role={role} key={i} className={columnsWidthOption}>
                      {item}
                    </CellTd>
                  ))}
                  {isColActionsCell && ActionsCellContent && (
                    <CellTd role={role} className={'action'}>
                      <ActionsCellContent
                        id={isTimeOffRequests ? data[index].id : element.id}
                        rowData={data[index]}
                        status={data[index].status}
                        userId={data[index].userId}
                      />
                    </CellTd>
                  )}
                </div>
              ))
          : []}
      </div>

      {isFooter && (
        <div className={styles['table-row-footer']}>
          <div>{footerTitle}</div>
          <div className={styles['table-footer-content']}>{footerContent}</div>
        </div>
      )}

      {isPagination && (
        <div className={styles.pagination}>
          <Pagination
            pageSize={itemsCountPerPage}
            total={totalItemsCount}
            current={activePage}
            onChange={handlePageChange}
            hideOnSinglePage={true}
            prevIcon={<Prev />}
            nextIcon={<Next />}
            className={'rc-pagination'}
          />
        </div>
      )}
    </div>
  )
}
