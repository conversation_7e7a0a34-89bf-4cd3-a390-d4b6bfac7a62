@import '../../assets/style/colors';
@import '../../assets/style/variables';
.table-wrapper-profile,
.table-wrapper-timeoff-requests {
  box-sizing: border-box;
  width: 100%;
  background: $white;
  border: 1px solid $light-grey;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 14px;
  overflow: hidden;
  padding: 0 25px 30px 25px;

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: $scroll-bar-background;
    box-shadow: inset 0 0 3px rgba(179, 179, 248, 0.2);
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb {
    background: $scroll-bar-thumb;
    border-radius: 5px;
    width: 6px;
    height: 64px;
  }

  .table-data-container-profile,
  .table-data-container-timeoff-requests {
    height: 425px;
    overflow-y: auto;
  }

  .table-data-container-requests {
    height: 425px;
    overflow-y: auto;
  }

  .table-data-container-new-request {
    height: 400px;
    overflow-y: auto;

    .table-cell-action {
      width: auto;
    }
  }

  .table-flex-container-profile ,
  .table-flex-container-timeoff-requests {
    display: flex;
    justify-content: space-between;
    padding: 15px 0 15px;
    text-align: left;
    div:nth-child(4) {
      button{
        margin: 0;
        padding: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: none;
        background: none;
        outline: none;
        svg{
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .table-row-header-profile ,
  .table-row-header-timeoff-requests {
    border-bottom: 1px solid $light-grey;
    color: $blue;
    font-weight: bold;

    .table-cell-th-components {
      min-height: 10px;
    }

    .table-cell-td-profile {
      font-weight: bold;
    }
  }
  .table-row-header-timeoff-requests {
    margin-right: 40px;
    //.table-cell-timeoff-requests{
    //  cursor: pointer;
    //}
  }
  .table-row-header-timeoff-requests-not-user{
    margin-right: 12px;
  }
  .table-row-data-profile:hover ,
  .table-row-data-timeoff-requests:hover {
    background-color: $Light-gray-bg-hover-table;
    cursor: pointer;
  }

  :global {
    .table-row-data-timeoff-requests-selected {
      background-color: $Light-gray-bg-hover-table;
      cursor: pointer;
    }
  }

  .table-cell-td-profile, .table-cell-th-profile ,
  .table-cell-td-timeoff-requests, .table-cell-th-timeoff-requests {
    padding: 5px 5px;
  }

  .table-cell-actions input {
    width: 100%;
  }

  .table-cell-th-profile ,
  .table-cell-th-timeoff-requests {
    font-weight: bold;
    text-align: left;
    cursor: pointer;

    span {
      padding-left: 5px;
    }
  }

  .sorting-disable {
    cursor: default;
  }

  .sorting-disable span {
    display: none;
  }

  .arrow-sort-desc img {
    transform: rotate(180deg);
  }

  .table-cell-td-profile ,
  .table-cell-td-timeoff-requests {
    text-align: left;
    display: inherit;
  }

  .table-row-header-components-disable {
    display: none;
  }

  .table-cell-action {
    width: 10%;
    text-align: center;

    button, button:active, button:focus {
      border: none;
      background-color: inherit;
      outline: none;
    }
  }

  .table-cell-profile ,
  .table-cell-timeoff-requests {
    width: 16.6%;
  }

  .table-cell-timeoff-requests {
    width: 20%;
  }
  .table-cell-timeoff-requests:first-child {
    width: 22%;
    padding-left: 30px;
  }
  .table-cell-timeoff-requests:nth-child(2) {
    //margin-right: -20px;
    width: 150px;
  }
  .table-cell-timeoff-requests:nth-child(5) {
    text-align: center;
  }
  .table-cell-timeoff-requests:last-child {
    width: 12%;
    text-align: center;
  }
  .table-cell-is-user {
    width: calc(100% / 6);
    cursor: pointer;
    &:first-child {
      width: 16.66%;
      padding-left: 0;
    }
    &:nth-child(2){
      width: 16.66%;
    }
    &:nth-child(5){
      width: 150px;
      text-align: center;
    }
    &:last-child{
      width: 140px;
      text-align: center;
      margin-right: 10px;
    }
  }

  .table-cell-is-admin{
    cursor: pointer;
    width: calc(100% / 7);
    &:first-child {
      width: 300px;
    }
    &:nth-child(2),
    &:nth-child(3),
    //&:nth-child(4),
    &:nth-child(6){
      width: 14.3%;
    }
    &:nth-child(4){
      width: 150px;
    }
    &:nth-child(5){
      width: 80px;
      text-align: center;
    }
    &:last-child{
      width: 110px;
      text-align: center;
      cursor: default;
    }
  }
  .table-cell-no-sort{
    cursor: default;
  }


  .table-row-footer {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    padding: 13px 0 16px;
    border-top: 1px solid $light-grey;

    .table-footer-content {
      color: $green;
    }
  }

  .pagination{
    margin-top: 20px;
  }
}

.table-wrapper-basket {
  box-sizing: border-box;
  width: 70%;
  border: 1px solid $light-grey;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 14px;
  background: $white;
  padding: 0 40px 0;
  margin: 10px;
  overflow: auto;

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: $scroll-bar-background;
    box-shadow: inset 0 0 3px rgba(179, 179, 248, 0.2);
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb {
    background: $scroll-bar-thumb;
    border-radius: 5px;
    width: 6px;
    height: 64px;
  }

  .table-data-container-basket {
    max-height: 425px;
    overflow-y: auto;
  }

  .table-row-data-basket {
    .table-cell-basket:nth-child(3){
      display: block;
      margin: 18px 0;
      font-weight: bold;
    }
    .table-cell-basket:nth-child(4){
      display: block;
      margin: 18px 0;
    }

    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
  }
  .table-cell-basket {
    width: 30%;
  }
  .table-row-header-basket {
    border-bottom: 1px solid $light-grey;
    color: $blue;
    font-weight: bold;

    .table-cell-th-components {
      min-height: 10px;
    }

    .table-cell-td-basket {
      font-weight: bold;
    }
  }
  .table-flex-container-basket {
    display: flex;
    justify-content: space-between;
    padding: 10px 0 10px;
    .table-cell-th-basket:nth-child(4){
      color: $red
    }
    .sorting-disable {
      cursor: default;
    }

    .sorting-disable span {
      display: none;
    }
    div:nth-child(4) {
      button{
        margin: 0;
        padding: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: none;
        background: none;
        outline: none;
        svg{
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

.table-cell-th-basket {
  font-weight: bold;
  cursor: pointer;
}

.table-cell-basket:nth-child(4) {
  width: 15%;
}


.red {
  color: $require-error-color;
  font-weight: 600;
}

.green {
  color: $approve-green;
  font-weight: 600;
}
.orange {
  color: $orange;
  font-weight: 600;
}

.black{
  color: $color-dark-blue;
  font-weight: 600;
}
