import React, { FC } from 'react'
import styles from './style.module.scss'
import cls from 'classnames'
import { isUser } from '../../utils/user'

// TODO --- refactor
export const CellTd: FC<{ className?: string; role?: string; children: React.ReactNode }> = ({
  children,
  className,
  role = '4'
}) => {
  const colorStyle =
    children === 'В ожидании'
      ? 'orange'
      : children === 'Утверждено'
      ? 'green'
      : children === 'Отказано'
      ? 'red'
      : children === 'Отменен'
      ? 'black'
      : null
  return (
    <div
      className={cls({
        [styles['table-cell-td']]: true,
        [styles[`table-cell-${className}`]]: !!className,
        [styles[`${colorStyle}`]]: true,
        [styles[`table-cell-is-user`]]: isUser(role),
        [styles[`table-cell-is-admin`]]: !isUser(role)
      })}
    >
      {children}
    </div>
  )
}
