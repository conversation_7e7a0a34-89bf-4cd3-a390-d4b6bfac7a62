import React, { FC } from 'react'
import arrow_sort_icon from 'assets/images/arrow_sort_icon.svg'
import styles from './style.module.scss'
import cls from 'classnames'
import { isUser } from '../../utils/user'

export const CellTh: FC<any> = (props: any) => {
  return (
    <div
      className={cls({
        [styles[`table-cell-${props.className}`]]: true,
        [styles[`table-cell-is-user`]]: isUser(props.role),
        [styles[`table-cell-is-admin`]]: !isUser(props.role),
        [styles[`table-cell-no-sort`]]: props.title === 'Часы'
      })}
      data-name={props.dataName}
      onClick={props.isSorting ? props.setOrderInTable : null}
    >
      {props.title}
      {props.title !== 'Часы' ? (
        <span
          className={
            props.nameIsSorted === props.dataName && props.isSorted === 'DESC'
              ? styles['arrow-sort-desc']
              : styles['arrow-sort-asc']
          }
        >
          <img src={arrow_sort_icon} alt="arrow" />
        </span>
      ) : null}
    </div>
  )
}
