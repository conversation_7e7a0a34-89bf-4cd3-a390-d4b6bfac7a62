import { FC } from 'react'

export interface ITableProps {
  columns?: any
  data?: any
  role?: string
  onSortedChange?: SortedChangeFunction
  isSorted?: any
  nameIsSorted?: any
  sorted?: any
  loading?: boolean
  PaginationComponent?: any
  Cell?: any
  onPageChange?: () => {}
  onPageSizeChange?: () => {}
  onFilteredChange?: () => {} // for filter
  setOrderInTable?: any
  handleOnChangeSort?: any
  activePage?: any
  handlePageChange?: any
  totalItemsCount?: any
  itemsCountPerPage?: any
  isColActionsCell?: boolean
  contentCollActionsCell?: JSX.Element
  isRowActionsCell?: boolean
  actionsTableHeader?: any
  columnsWidthOption?: string
  tableDataHeightOption?: string
  isFooter?: boolean
  footerTitle?: string
  footerContent?: string
  isPagination?: boolean
  totalQuantity?: number
  ActionsCellContent?: FC<any>
  onRowClick?: (id: number) => void
  isTimeOffRequests?: boolean
}

export type SortedChangeFunction = (
  newSorted: SortingRule[],
  column: any,
  additive: boolean
) => void

interface SortingRule {
  id: string
  desc: boolean
}

export interface IColumnProps {
  Header: string
  headerClassName?: string
  accessor: string
  minWidth: number
  resizable: boolean
}

export interface ITablePropsCreator {
  columns: IColumnProps[]
  data: any
}

export interface IDataArr {
  Header: string
  accessor: string
  headerClassName: string
  Cell?: any
}
