import React, { FC, useState } from 'react'
import { useSelector } from 'react-redux'
import cls from 'classnames'
import { TState } from 'store'
import { bidRate } from 'globalConfigs'
import { useAvailableFunds } from 'hooks/useAvailableFunds'
import { But<PERSON> } from 'components/UiKit/Button'
import PurchaseBidsModal from 'components/SubcontractModals/PurchaseBidsModal/PurchaseBidsModal'

import styles from './InvitationsDataBlock.module.scss'

interface IInvitationsDataBlockProps {
  className?: string
}

export const InvitationsDataBlock: FC<IInvitationsDataBlockProps> = ({ className }) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const { availableCalls } = useAvailableFunds()

  const [modalState, setModalState] = useState(false)

  return (
    <>
      <div className={cls([styles['invitations-block']], { [className || '']: className })}>
        <div className={styles['available-calls']}>
          {words['user.interviewInvitation.availableInvitations']}:
          <span className={cls([styles['value'], { [styles['not']]: !availableCalls }])}>
            {availableCalls}
          </span>
          <span className={styles['price']}>
            {(
              words['user.interviewInvitation.bidsPerInvitation'] || '<VALUE> bids per invitation'
            ).replace('<VALUE>', bidRate.callsRate)}
          </span>
        </div>

        <Button size={'md'} color={'green'} onClick={() => setModalState(true)}>
          <span>{words['user.subcontract.buy']}</span>
        </Button>
      </div>

      {/* Modals */}
      {modalState && <PurchaseBidsModal onClose={() => setModalState(false)} />}
    </>
  )
}
