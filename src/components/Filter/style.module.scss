@import '../../assets/style/colors';
@import "../../assets/style/variables";

.filter-container {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  line-height: 16px;
  background: $color-white;
  border: 1px solid $light-grey;
  box-sizing: border-box;
  box-shadow: 0 0 20px $box-shadow;
  border-radius: 14px;
  color: $grey;
  text-align: left;
  padding: 16px 25px;
  margin-bottom: 20px;

  .filter-components {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .btn-container {
    display: flex;
    justify-content: space-between;
    width: 210px;
    margin-left: 20px;

    button {
      height: 30px;
      width: 100px;
      margin-top: 18px;
      font-size: $font-size-xs;
      outline: none;

    }
  }
}

@media (max-width: 1410px) {
  .store {
    display: block;
  }
  .btn-container {
    float: right;
  }
  .filter-container {
    height: 140px;
  }


}
