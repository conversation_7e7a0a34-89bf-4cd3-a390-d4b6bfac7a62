import React, { FC } from 'react'
import { IFilterProps } from './model'
import { ReactComponent as IconCancel } from 'assets/images/cancel_icon.svg'
import 'react-datepicker/dist/react-datepicker.css'
import styles from './style.module.scss'
import '../DatePickerRange/style.scss'
import { Button } from 'components/UiKit/Button'
import cn from 'classnames'
import { useSelector } from 'react-redux'
import { TState } from 'store'

const Filter: FC<IFilterProps> = ({
  components,
  className,
  handleFilterSubmit,
  handleFilterCancel,
  isDischargeBtn = false,
  loading
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  return (
    <form
      className={cn(styles['filter-container'], styles[className])}
      onSubmit={handleFilterSubmit}
    >
      {components}

      <div className={styles['btn-container']}>
        {isDischargeBtn && (
          <Button type="button" className={'btn-discharge'} onClick={handleFilterCancel}>
            <IconCancel />
          </Button>
        )}

        <Button type="submit" className={'btn-primary'} disabled={loading}>
          {words['component.filter.submit']}
        </Button>
      </div>
    </form>
  )
}

export default Filter
