import React, { FC, useRef } from 'react'
import { createRoot } from 'react-dom/client'
import { useSelector } from 'react-redux'

import { TState } from 'store'
import { ITaskInvoice } from 'models'
import { generateSubcontractInvoicePdf } from 'helpers/generatePdf/generateSubcontractInvoicePdf'
import { BaseButton } from 'components/UiKit/Button/BaseButton'
import Modal from 'components/Modal'
import InvoiceTask from 'components/InvoiceTask/InvoiceTask'

import styles from './style.module.scss'

type TModalProps = {
  closeCallback: () => void
  invoice: ITaskInvoice
}

const TaskInvoiceModal: FC<TModalProps> = ({ closeCallback, invoice }) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const cancel_w = words['user.subcontract.cancel']
  const download_w = words['user.subcontract.task.downloadInvoice']

  const downloadRef = useRef<any>(null)

  const closeModal = () => {
    closeCallback()
  }
  const dowloadInvoice = async () => {
    const container = document.createElement('div')
    Object.assign(container.style, {
      position: 'absolute',
      top: '-9999px',
      left: '-9999px',
      width: 'auto',
      height: 'auto',
      opacity: '0',
      pointerEvents: 'none'
    })

    document.body.appendChild(container)
    const root = createRoot(container)
    root.render(<InvoiceTask invoice={invoice} />)

    await new Promise<void>(resolve => {
      const observer = new MutationObserver(() => {
        observer.disconnect()
        resolve()
      })

      observer.observe(container, {
        childList: true,
        subtree: true
      })
    })

    if (!container.firstChild) return
    generateSubcontractInvoicePdf(container.firstChild as HTMLElement, invoice.id)

    root.unmount()
    container.remove()

    closeCallback()
  }

  return (
    <Modal isShow={true} onClose={closeModal}>
      <div className={styles.container}>
        <div className={styles['invoice-block']}>
          <InvoiceTask ref={downloadRef} invoice={invoice} />
        </div>
        <div className={styles['button-container']}>
          <BaseButton
            style={{ cursor: 'pointer' }}
            children={cancel_w}
            outline={true}
            size={'lgs'}
            onClick={closeModal}
          />
          <BaseButton
            style={{ cursor: 'pointer' }}
            children={download_w}
            size={'lgs'}
            onClick={dowloadInvoice}
          />
        </div>
      </div>
    </Modal>
  )
}

export default TaskInvoiceModal
