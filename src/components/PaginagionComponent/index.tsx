import React, { FC, useState } from 'react'
import Select from 'components/Select'
import DefaultButton from 'components/DefaultButton'
import IProps from './model'

// This component was taken from example.
const PaginationComponent: FC<IProps> = ({
  pages,
  pageSizeOptions,
  onPageSizeChange,
  nextText,
  page,
  onPageChange,
  previousText
}) => {
  const activePage = page + 1
  const filterPages = (visiblePagesArr: any, totalPages: any) => {
    return visiblePagesArr.filter((pageNum: any) => pageNum <= totalPages)
  }
  const getVisiblePages = (pageNum: any, total: any) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total)
    } else {
      if (pageNum % 5 >= 0 && pageNum > 4 && pageNum + 2 < total) {
        return [1, pageNum - 1, pageNum, pageNum + 1, total]
      } else if (pageNum % 5 >= 0 && page > 4 && pageNum + 2 >= total) {
        return [1, total - 3, total - 2, total - 1, total]
      } else {
        return [1, 2, 3, 4, 5, total]
      }
    }
  }

  const [visiblePages, changePageState] = useState(getVisiblePages(null, pages))

  const changePage = (pageNum: any) => {
    if (pageNum === pageNum + 1) {
      return
    }
    onPageChange(pageNum - 1)
    changePageState(getVisiblePages(pageNum, pages))
  }

  return (
    <div className="Table__pagination">
      <Select
        name="paginationSelect"
        data={pageSizeOptions}
        onChange={e => {
          onPageSizeChange(e.currentTarget.value)
        }}
      />
      <div className="Table__prevPageWrapper">
        <DefaultButton
          className="Table__pageButton"
          onClick={() => {
            if (activePage === 1) {
              return
            }
            changePage(activePage - 1)
          }}
          disabled={activePage === 1}
        >
          {previousText}
        </DefaultButton>
      </div>
      <div className="Table__visiblePagesWrapper">
        {visiblePages.map((pageNum: any, index: any, array: any) => {
          return (
            <DefaultButton
              key={pageNum}
              className={
                activePage === pageNum
                  ? 'Table__pageButton Table__pageButton--active'
                  : 'Table__pageButton'
              }
              onClick={() => changePage(pageNum)}
            >
              {array[index - 1] + 2 < pageNum ? `...${pageNum}` : pageNum}
            </DefaultButton>
          )
        })}
      </div>
      <div className="Table__nextPageWrapper">
        <DefaultButton
          className="Table__pageButton"
          onClick={() => {
            if (activePage === pages) {
              return
            }
            changePage(activePage + 1)
          }}
          disabled={activePage === pages}
        >
          {nextText}
        </DefaultButton>
      </div>
    </div>
  )
}

export default PaginationComponent
