import React, { <PERSON> } from 'react'
import DatePicker from 'react-datepicker'
import IProps from './model'
import styles from './style.module.scss'
import './style.scss'
import { config } from 'globalConfigs'
import { useSelector } from 'react-redux'
import { TState } from 'store'

const DatePickerRange: FC<IProps> = ({
  startFilteringDate,
  endFilteringDate,
  handleStartFilteringDateChange,
  handleEndFilteringDateChange,
  selectsStart
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  return (
    <div className={styles['data-picker-container']}>
      {words['manager.dayOffTracker.details.requestDate']}
      <br />
      <div className={styles['data-picker-body']}>
        <span className={styles['data-picker-text']}>c</span>
        <DatePicker
          dateFormat={config.dateFormat}
          selected={startFilteringDate}
          onChange={handleStartFilteringDateChange}
          selectsStart={selectsStart}
          startDate={startFilteringDate}
          // minDate={startFilteringDate}
          maxDate={new Date()}
        />
        <span className={styles['data-picker-text']}>
          {words['user.pointsSystem.market.table.to']}
        </span>
        <DatePicker
          dateFormat={config.dateFormat}
          selected={endFilteringDate}
          onChange={handleEndFilteringDateChange}
          selectsEnd={selectsStart}
          startDate={startFilteringDate}
          minDate={startFilteringDate}
          maxDate={new Date()}
        />
      </div>
    </div>
  )
}

export default DatePickerRange
