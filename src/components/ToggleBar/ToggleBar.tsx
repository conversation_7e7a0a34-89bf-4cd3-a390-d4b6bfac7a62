import React, { FC } from 'react'
import { IProps } from './ToggleBar.model'
import styles from './ToggleBar.module.scss'
import { Tooltip } from 'react-tooltip'

export const ToggleBar: FC<IProps> = ({
  data,
  name,
  checked,
  onChange,
  style = 1,
  className = ''
}) => {
  const wrapperStyle = { 1: 'radio-toolbar', 2: 'cell-toolbar' }[style] || ''

  return (
    <div
      className={`${styles.togglebar} ${className} ${
        styles[wrapperStyle] ? styles[wrapperStyle] : ''
      }`}
    >
      {Object.values(data).map((item, index) => (
        <div key={index} data-tip="true" id={`tabTooltip_${index}`}>
          <input
            type="radio"
            id={item.value}
            name={name}
            value={item.value}
            checked={item.value === checked}
            onChange={() => onChange(item.value)}
            disabled={item.disabled}
          />
          <label htmlFor={item.value}>{item.title}</label>
          <div className={styles.cover} />
          <Tooltip
            className="tooltip"
            id={`tabTooltip_${index}`}
            anchorSelect={`#tabTooltip_${index}`}
            place="bottom"
          >
            {item.tip || ''}
          </Tooltip>
        </div>
      ))}
    </div>
  )
}
