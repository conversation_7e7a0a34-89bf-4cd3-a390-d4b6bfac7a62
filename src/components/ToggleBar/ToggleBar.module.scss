@import 'assets/style/colors';
@import 'assets/style/variables';

$transition-base: all 265ms;

.togglebar {
  height: 32px;
  position: relative;

  div {
    display: inline-block;
  }

  & input[type="radio"] {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
    left: 0;
    top: 0;

    // &:focus
    &:checked + label {
      background: transparent;
      border-color: $blue;
      color: $blue;
      transition: all 0s;
    }
  }

  & label {
    height: 30px;
    padding: 0 $toggle-bar-label-side-paddings;
    display: inline-flex;
    justify-content: center;
    font-weight: bold;
    text-align: center;
    margin: 0;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.1s;
    position: relative;
    font-family: 'Open Sans', sans-serif;
    font-style: normal;
    font-size: 16px;
    line-height: 30px;
    color: $grey;
    background: inherit;

    &:hover {
      color: $blue;
      transition: $transition-base;
    }

    &:active {
      color: $white;
      transition: $transition-base;
    }
  }

  &.cell-toolbar {
    height: 50px;

    &::after {
      content: '';
      display: block;
      position: absolute;
      bottom: 0;
      left: 0;
      height: 2px;
      width: 100%;
      background: $blue;
    }

    & > div {
      position: relative;

      &:first-of-type label {
        border-left: none !important;
        border-top-left-radius: 0 !important;
      }

      &:first-of-type .cover::before {
        display: none !important;
      }
    }
  }

  &.cell-toolbar label {
    padding: 0 25px;
    height: 50px;
    line-height: 50px;
    box-sizing: border-box;
  }

  // :focus
  &.cell-toolbar input[type="radio"]:checked + label {
    border: 2px solid $blue;
    border-bottom: none;
    border-radius: 8px;
  }

  // :focus
  &.cell-toolbar input[type="radio"]:checked ~ .cover {
    position: absolute;
    bottom: -2px;
    left: -5px;
    background: $scroll-bar-background;
    width: calc(100% + 10px);
    height: 10px;
    z-index: 1;

    &::before,
    &::after {
      content: '';
      display: block;
      position: absolute;
      width: 10px;
      height: 10px;
      bottom: 2px;
      background: $scroll-bar-background;
      border-bottom: 2px solid $blue;
      border-radius: 8px;
    }

    &::before {
      left: -5px;
      border-right: 2px solid $blue;
      border-top-right-radius: 0;
      border-bottom-left-radius: 0;
    }

    &::after {
      right: -5px;
      border-left: 2px solid $blue;
      border-top-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}