import React, { <PERSON> } from 'react'
import Input from 'components/Input'
import styles from './style.module.scss'
import { useSelector } from 'react-redux'
import { TState } from 'store'

export type TInputRangeProps = {
  handleOnChangeStart?: (e: any) => void
  handleOnChangeEnd?: (e: any) => void
  pointsStartValue?: string
  pointsEndValue?: string
}

export const InputRange: FC<TInputRangeProps> = ({
  pointsStartValue,
  pointsEndValue,
  handleOnChangeStart,
  handleOnChangeEnd
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <div className={styles['points-filter-container']}>
      <div>{words['component.inputRange.transaction']}</div>
      <div className={styles['points-input-container']}>
        <Input
          className={'filter-input'}
          name={'minPoints'}
          value={pointsStartValue}
          onChange={handleOnChangeStart}
        />
        <span className={styles['range-span']}> - </span>
        <Input
          className={'filter-input'}
          name={'maxPoints'}
          value={pointsEndValue}
          onChange={handleOnChangeEnd}
        />
      </div>
    </div>
  )
}
