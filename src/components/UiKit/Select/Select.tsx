import React, { FC, useEffect, useRef, useState } from 'react'
import { default as ReactSelect, SelectInstance, components as rComponents } from 'react-select'
import cls from 'classnames'
import styles from './Select.module.scss'

export type TSelectProps = {
  innerRef?: null | SelectInstance
  isAddNewItem?: boolean
  isInvalid?: boolean
  styles?: any
  isDisabled?: boolean
}

const Menu = (props: any) => {
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  return (
    <rComponents.Menu {...props}>
      {props.children}
      <div className={styles['input-container']}>
        <input
          className={cls()}
          ref={inputRef}
          disabled={props.disabled}
          onKeyDown={event => {
            if (event.keyCode === 13) {
              event.stopPropagation()
              alert(`your added - ${inputValue}`)
            }
          }}
          onClick={() => {
            const elem = inputRef.current

            if (elem !== null) {
              elem.focus()
            }
          }}
          onChange={event => setInputValue(event.target.value)}
          value={inputValue}
        />

        <button
          onClick={() => {
            alert(`your added - ${inputValue}`)
            // props.selectRef.current.select.onInputBlur(event)
          }}
        >
          +
        </button>
      </div>
    </rComponents.Menu>
  )
}

export const Select: FC<TSelectProps> = ({
  innerRef = null,
  isAddNewItem,
  styles: customStyles,
  isInvalid,
  ...props
}) => {
  const [selectRef, setSelectRef] = useState<any>(innerRef)

  const newRef = useRef<SelectInstance | null>(null)

  useEffect(() => {
    if (!innerRef) {
      setSelectRef(newRef)
    }
  }, [innerRef])

  const colorBorder = props.isDisabled
    ? { borderColor: '#9A9A9A' }
    : isInvalid
    ? { borderColor: '#D12A6C' }
    : { borderColor: '#3737ED' }

  const baseStyles = {
    container: (style: any) => ({
      ...style,
      height: '30px',
      marginTop: '5px',
      color: '#333333',
      button: { height: '27px', width: '90px' }
    }),
    control: (style: any) => ({
      ...style,
      ...colorBorder,
      borderRadius: '10px',
      minHeight: '30px',
      height: '30px',
      backgroundColor: 'hsl(0,0%,100%)',
      ':hover': { borderColor: '#3737ED' }
    }),
    singleValue: (style: any) => ({ ...style, div: { span: { display: 'none' } } }),
    indicatorsContainer: (style: any) => ({ ...style, div: { padding: '4px' } }),
    indicatorSeparator: (style: any) => ({ ...style, display: 'none' })
  }
  return (
    <ReactSelect
      ref={selectRef as any}
      {...props}
      styles={{ ...baseStyles, ...customStyles }}
      components={{
        Menu: isAddNewItem
          ? (menuProps: any) => <Menu {...menuProps} selectRef={selectRef} />
          : undefined
      }}
      backspaceRemovesValue={!isAddNewItem}
      openMenuOnFocus={true}
    />
  )
}
