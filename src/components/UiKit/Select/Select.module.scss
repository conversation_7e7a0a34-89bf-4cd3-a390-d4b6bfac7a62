@import '../../../assets/style/colors';
@import '../../../assets/style/variables';


.base {
  select {
    width: 100%;
    height: 30px;
    border: 1px solid $blue;
    box-sizing: border-box;
    border-radius: 10px;
    background: $color-white;
    box-shadow: 0 0 20px $box-shadow;
    color: $dark-grey;
    margin: 5px 10px 0 0;
    padding: 5px 20px 5px 5px;
    background: url(../../../assets/images/arrow_icon.svg) no-repeat;
    background-position: calc(100% - 5px);
    font-size: 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
}

.filter-select {
  width: 100%;
  select {
    width: 100%;
  }
}

.filter-select-disable {
  display: none;
}

.invalid {
  color: $invalid-red
}

.invalid select {
  border: 1px solid $invalid-red;
}


.input-container {
  display: flex;
  justify-content: space-between;
}
