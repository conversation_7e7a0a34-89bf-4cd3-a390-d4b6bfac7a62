import React from 'react'
import { Field } from 'react-final-form'
import styles from './FieldFactory.module.scss'
import { Input } from 'components/UiKit/Inputs'
import { SearchPlaces } from 'components/UiKit/SearchPlaces'
import { Select } from 'Select'
import { RedNote } from 'components/RedNote'
import validation from 'utils/validation'
import { TSelectOption } from 'components/Select'
import { DatePicker } from '../DatePicker'
import { Textarea } from 'components/Textarea'
import { TFieldConfigRow, TFieldFactoryProps } from './FieldFactory.model'
import { handleDefaultOption } from './libs/helpers/handleDefaultOption.helper'
import { InputPhoneNumber } from '../InputPhoneNumber'

const FieldFactory: any = ({
  disabled,
  config,
  form,
  words,
  currentLanguage,
  labelClassName,
  className
}: TFieldFactoryProps) => {
  return (
    <div className={className}>
      {config.map((row: TFieldConfigRow, arrIndex: number) => {
        return (
          <div key={arrIndex} className={styles.row} style={row.style && row.style}>
            {row.items.map((item: any, index: number) => {
              const validatorNames: any = {
                required: item.required,
                isPhoneNumber: item.isPhoneNumber,
                isMin: item.minValue,
                isMinLength: item.minLength,
                isEmail: item.isEmail,
                isMaxLength: item.maxLength,
                isNumber: item.isNumber,
                notEmptyArr: item.notEmptyArr
              }
              const validators: any = {
                required: validation.required(words['user.requiredMessage']),
                // isPhoneNumber: validation.isPhoneNumber(),
                isMin: validation.min(validatorNames.isMin),
                isMinLength: validation.minValue(validatorNames.isMinLength),
                isEmail: validation.isEmail(),
                isMaxLength: validation.maxValue(validatorNames.isMaxLength),
                isNumber: validation.isNumber(),
                notEmptyArr: validation.notEmptyArr()
              }
              const isValidate: any = Object.values(validatorNames).find(validatorType => {
                return validatorType && true
              })

              const setValidators = () => {
                const setOfValidators: any = []
                Object.keys(validatorNames).forEach(validator => {
                  if (validatorNames[validator]) {
                    setOfValidators.push(validators[validator])
                  }
                })
                return setOfValidators
              }

              return (
                <Field
                  key={index}
                  name={item.name}
                  validate={isValidate && validation.composeValidators(...setValidators())}
                >
                  {({ input, meta }) => {
                    const componentInfo = item.component({ input, meta, form, disabled })
                    let component = <></>
                    switch (componentInfo.type) {
                      case 'input':
                        component = (
                          <Input
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            disabled={disabled}
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'inputPhoneNumber':
                        component = (
                          <InputPhoneNumber
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            disabled={disabled}
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'searchPlaces':
                        component = (
                          <SearchPlaces
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'select':
                        component = (
                          <Select
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            onChange={(value: TSelectOption) => {
                              return form.change(input.name, (value as TSelectOption).value)
                            }}
                            emptyMessage={words['noOption']}
                            value={handleDefaultOption(
                              componentInfo.props.options.find(
                                (element: TSelectOption) => element.value == input.value
                              )
                            )}
                            isDisabled={disabled}
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'multiSelect':
                        component = (
                          <Select
                            {...input}
                            disabled={disabled}
                            isInvalid={!!meta.error && meta.touched}
                            errorMessage={meta.error}
                            onChange={(value: [TSelectOption]) => {
                              return form.change(
                                input.name,
                                value && value.map(i => ({ value: i.value, label: i.label }))
                              )
                            }}
                            emptyMessage={words['noOption']}
                            value={
                              input.value &&
                              input.value.map((valueItem: { value: string; label: string }) => {
                                return componentInfo.props.options.find(
                                  (element: TSelectOption) => {
                                    return Number(element.value) === Number(valueItem.value)
                                  }
                                )
                              })
                            }
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'datePicker':
                        component = (
                          <DatePicker
                            disabled={disabled}
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            locale={currentLanguage}
                            name={input.name}
                            onChange={event => {
                              if (event) {
                                form.change(input.name, event)
                              }
                            }}
                            {...componentInfo.props}
                          />
                        )
                        break
                      case 'textarea':
                        component = (
                          <Textarea
                            {...input}
                            isInvalid={meta.error && meta.submitFailed}
                            errorMessage={meta.error}
                            {...componentInfo.props}
                          />
                        )
                    }

                    return (
                      <div
                        className={item.inputWrapperClassName ? item.inputWrapperClassName : ''}
                        style={
                          !item.inputWrapperClassName
                            ? { width: '100%', marginBottom: '32px' }
                            : undefined
                        }
                      >
                        {!!item.label && (
                          <div className={styles['label']}>
                            <label className={labelClassName}>
                              {item.label} {item.required && <RedNote />}
                            </label>
                          </div>
                        )}
                        {component}
                      </div>
                    )
                  }}
                </Field>
              )
            })}
          </div>
        )
      })}
    </div>
  )
}

export default FieldFactory
