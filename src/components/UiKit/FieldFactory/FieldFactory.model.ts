import { TWords } from 'elements/SideBar/SideBar.config'

export type TFieldConfigRow = {
  style?: any
  items: TFieldConfigItem[]
}

export type TFieldConfigItem = {
  name: string
  label?: string
  inputWrapperClassName?: string
  isPhoneNumber?: boolean
  required?: boolean
  minValue?: number
  minLength?: number
  maxLength?: number
  isEmail?: boolean
  isNumber?: boolean
  component: ({  }: TFieldConfigComponentArgs) => TFieldConfigComponent
  disabled?: boolean
}

export type TFieldConfigComponentArgs = {
  meta?: any
  form?: any
  input?: any
}

export type TFieldConfigComponent = {
  type: string
  props: TFieldConfigComponentProps
}

export type TFieldConfigComponentProps = {
  variant?: string
  placeholder?: string
  isInvalid?: string
  errorMessage?: string
  autoComplete?: string
  label?: any
}

export type TFieldFactoryProps = {
  config: TFieldConfigRow[]
  form: any
  words: TWords
  currentLanguage: string
  disabled?: boolean
  defaultValue: any
  labelClassName?: string
  className?: string
}
