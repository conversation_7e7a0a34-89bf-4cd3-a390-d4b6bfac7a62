import React, { <PERSON> } from 'react'
import cls from 'classnames'
import styles from './RequestTypeCell.module.scss'
import { ERequestStatus, STATUS_OF_REQUESTS_TIME_OFF } from 'globalConfigs'
import { useSelector } from 'react-redux'
import { TState } from 'store'

type TTableProps = {
  content: number
}

export const RequestTypeCell: FC<TTableProps> = ({ content }) => {
  const words = useSelector((state: TState) => state.global.language.words)

  return (
    <div
      className={cls(styles.base, {
        [styles.pending]: content === ERequestStatus.PENDING,
        [styles.approve]: content === ERequestStatus.APPROVED,
        [styles.reject]: content === ERequestStatus.REJECTED,
        [styles.cancel]: content === ERequestStatus.CANCELED
      })}
    >
      {STATUS_OF_REQUESTS_TIME_OFF(words)[content]}
    </div>
  )
}
