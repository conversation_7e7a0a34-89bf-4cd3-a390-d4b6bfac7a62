import React, { FC } from 'react'
import { default as ReactSelect, StylesConfig } from 'react-select'
import IProps from './model'
import styles from './style.module.scss'
import classNames from 'classnames'

export type TSelectOption = {
  value: string
  label: string
  date?: string
  index?: number
}

const Select: FC<IProps> = ({
  name,
  data,
  className,
  title,
  closeMenuOnSelect,
  components,
  customStyles,
  defaultValue,
  isDisabled,
  value,
  inputValue,
  onChange,
  isInvalid,
  placeholder
}) => {
  const selectClass = classNames(styles.base, styles[`${className}`], {
    [styles.invalid]: isInvalid
  })

  const cursor = isDisabled ? 'not-allowed' : 'pointer'

  const colorBorder = isDisabled
    ? { borderColor: '#9A9A9A' }
    : isInvalid
    ? { borderColor: '#D12A6C' }
    : { borderColor: '#3737ED' }

  const baseStyles: StylesConfig = {
    container: style => ({
      ...style,
      height: '30px',
      marginTop: '5px',
      color: '#333333',
      button: { height: '27px', width: '90px' }
    }),
    control: style => ({
      ...style,
      ...colorBorder,
      borderRadius: '10px',
      minHeight: '30px',
      height: '30px',
      backgroundColor: 'hsl(0,0%,100%)',
      cursor,
      ':hover': { borderColor: '#3737ED' }
    }),
    singleValue: style => ({ ...style, div: { span: { display: 'none' } } }),
    indicatorsContainer: style => ({ ...style, div: { padding: '4px' } }),
    indicatorSeparator: style => ({ ...style, display: 'none' })
  }

  return (
    <label className={selectClass} style={{ cursor }}>
      <span>{title}</span>
      <ReactSelect
        name={name}
        onChange={onChange}
        closeMenuOnSelect={closeMenuOnSelect}
        components={{ DropdownIndicator: components }}
        value={value}
        styles={{ ...baseStyles, ...customStyles }}
        defaultValue={defaultValue}
        isDisabled={isDisabled}
        inputValue={inputValue}
        options={data}
        placeholder={placeholder}
      />
    </label>
  )
}

export default Select
