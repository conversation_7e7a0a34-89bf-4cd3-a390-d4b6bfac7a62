@import '../../assets/style/colors';
@import "../../assets/style/variables";

.filter-select {
    width: 100%;

  select {
    width: 100%;
    min-width: 80px;
    height: 30px;
    border: 1px solid $blue;
    box-sizing: border-box;
    border-radius: 10px;
    background: $color-white;
    box-shadow: 0 0 20px $box-shadow;
    color: $dark-grey;
    margin: 5px 10px 0 0;
    padding: 5px 20px 5px 5px;
    background: url(../../assets/images/arrow_icon.svg) no-repeat;
    background-position: calc(100% - 5px);
    font-size: 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
}

.filter-select-disable {
  display: none;
}

.desc-selects {
  width: 50%;

  select {
    border: 1px solid $blue;
    width: 137px;
    height: 30px;
    box-sizing: border-box;
    border-radius: 10px;
    background: $color-white;
    box-shadow: 0 0 20px $box-shadow;
    color: $dark-grey;
    margin: 5px 10px 0 0;
    padding: 5px 20px 5px 5px;
    appearance: none;
    background: url(../../assets/images/arrow_icon.svg) no-repeat;
    background-position: calc(100% - 5px);
    font-size: 12px;
  }
}

.time-select {
  select {
    width: 72px;
    height: 30px;
    border: 1px solid $blue;
    box-sizing: border-box;
    color: $dark-grey;
    border-radius: 10px;
    background: $color-white;
    padding: 5px 20px 5px 10px;
    background: url(../../assets/images/time_icon.svg) no-repeat;
    background-position: calc(100% - 5px);
    font-size: 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
  }

  select[disabled] {
    border-color: $grey;
    background: url(../../assets/images/time_icon_disable.svg) no-repeat;
    background-position: calc(100% - 5px);
    color: $grey;
  }
}
