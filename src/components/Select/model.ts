interface IProps {
  name: string
  columns?: any
  key?: any
  data?: any
  value?: any
  inputValue?: any
  label?: string
  onChange?: (e: any) => void
  className?: string
  title?: string
  customStyles?: any
  defaultValue?: IDefaultFilterOption
  selectedDefaultFilterOption?: boolean
  disabledDefaultFilterOption?: boolean
  hiddenDefaultFilterOption?: boolean
  isInvalid?: boolean
  closeMenuOnSelect?: boolean
  components?: any
  isDisabled?: boolean
  placeholder?: string
}
export default IProps

interface IDefaultFilterOption {
  value: string
  label: string
}

/*
interface IProps {
  name: string
  value?: string
  columns?: any
  key?: any
  data?: any
  onChange?: (e: any) => void
  className?: string
  title?: string
  defaultFilterOption?: IDefaultFilterOption
  selectedDefaultFilterOption?: boolean
  disabledDefaultFilterOption?: boolean
  hiddenDefaultFilterOption?: boolean
  handleSelectChange?: (e: any) => void
  disabled?: boolean
}
export default IProps

interface IDefaultFilterOption {
  value: string
  label: string
}
*/
