import { Props } from 'react-select/dist/declarations/src/Select'
import { GroupBase, Options } from 'react-select/dist/declarations/src/types'
import { CSSProperties, ReactNode } from 'react'
import { SelectInstance, StylesConfig } from 'react-select'

export type TSelectOption<Rest = {}> = {
  value: string
  label: string
} & Rest

export type TSelectProps<OptionType extends TSelectOption = TSelectOption> = {
  defaultValue?: TSelectOption
  addCategory?: any
  styles?: StylesConfig
  style?: CSSProperties
  getData?: any
  label?: string
  isMulti?: boolean
  isAsync?: boolean
  inRef?: null | React.RefObject<SelectInstance>
  isAddNewItem?: boolean
  isCheckboxesStyle?: boolean
  isSelectedAll?: boolean
  isInvalid?: boolean
  errorMessage?: string
  loadOptions?: (inputValue: string, callback: (options: Options<OptionType>) => void) => void
  emptyMessage?: ReactNode
  title?: string
} & Partial<Props<OptionType, boolean, GroupBase<OptionType>>>
