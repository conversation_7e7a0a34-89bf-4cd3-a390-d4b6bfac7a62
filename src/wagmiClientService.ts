/* eslint-disable prettier/prettier */
import {
  getWalletClient,
  getPublicClient,
  getAccount
} from '@wagmi/core'
import { wagmiConfig } from 'elements/PrivatRoute/components/wagmiConfig'
import { EMessageCodes } from 'types/EMessageCodes'

export type ClientService = {
  address?: `0x${string}` | null
  signer: Awaited<ReturnType<typeof getWalletClient>> | null
  client: ReturnType<typeof getPublicClient>
  mode: 'wallet' | 'rpc'
}

export const getClientService = async (): Promise<ClientService> => {
  const { address, status } = getAccount(wagmiConfig)

  const publicClient = getPublicClient(wagmiConfig)

  if (status === 'connected' && address) {
    const signer = await getWalletClient(wagmiConfig)
    if (!signer) {
      throw new Error(EMessageCodes.ERR_META_MASK_NOT_FOUND)
    }

    return {
      address,
      signer,
      client: publicClient,
      mode: 'wallet'
    }
  }

  // fallback: only reading
  return {
    address: null,
    signer: null,
    client: publicClient,
    mode: 'rpc'
  }
}

export const ensureWalletMode = async (): Promise<void> => {
  const { mode } = await getClientService()
  if (mode !== 'wallet') {
    throw new Error(EMessageCodes.ERR_META_MASK_NOT_FOUND)
  }
}

export const getWalletAddressOrThrow = async (): Promise<`0x${string}`> => {
  const { mode, address } = await getClientService()
  if (mode !== 'wallet' || !address) {
    throw new Error(EMessageCodes.ERR_META_MASK_NOT_FOUND)
  }
  return address
}
