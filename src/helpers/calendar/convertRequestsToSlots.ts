import { ICalendarEventRequest, ICalendarEventSlot } from 'models/calendar-models'
import { convertToSlot } from './convertToSlot'

export const convertRequestsToSlots = (requests: ICalendarEventRequest[]): ICalendarEventSlot[] => {
  if (!requests.length) return []

  return requests.reduce(
    (acc, request) => [...acc, ...request.suggestedDates.map(date => convertToSlot(request, date))],
    [] as ICalendarEventSlot[]
  )
}
