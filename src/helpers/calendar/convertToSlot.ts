import { EEventRequestType, ICalendarEventRequest, ISuggestedDates } from 'models/calendar-models'

export const convertToSlot = (request: ICalendarEventRequest, date: ISuggestedDates) => {
  let withUser = ''
  if (request.invited) withUser = request.invited.fullName
  else if (request.author) withUser = request.author.fullName

  return {
    authorId: request.authorId,
    id: `${request.id}_${date.slotId}`,
    invitedId: request.invitedId,
    taskId: request.taskId || 0,
    title: request.task ? request.task.title : '***',
    description: request.task ? request.task.description : '*********',
    start: new Date(date.start),
    end: new Date(date.end),
    slotId: date.slotId,
    isMyEvent: request.isMyEvent,
    isGeneralEvent: request.isGeneralEvent,
    isForeignEvent: request.isForeignEvent,
    isCustomer: request.isCustomer,
    isUser: request.isUser,
    withUser,
    type: EEventRequestType.REQUESTED
  }
}
