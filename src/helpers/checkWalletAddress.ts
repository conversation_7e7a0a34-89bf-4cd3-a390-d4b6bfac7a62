import { TState } from 'store'
import { EMessageCodes } from 'types/EMessageCodes'
import { getClientService } from 'wagmiClientService'

// Compare active wallet and wallet from DB

export const checkWalletAddressWagmi = async (data: TState) => {
  const user = data.auth.data

  const clientService = await getClientService()

  if (!clientService.client) {
    throw new Error(EMessageCodes.ERR_META_MASK_NOT_FOUND)
  }

  const isRightWallet = user.walletAddress === clientService.address

  return { ...clientService, isRightWallet }
}
