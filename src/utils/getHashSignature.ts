/* eslint-disable prettier/prettier */
import { signMessage } from 'viem/actions'
import { getClientService } from 'wagmiClientService'

export const getHashSignature = async (
  // eslint-disable-next-line prettier/prettier
  address: string | `0x${string}`,
  messageHash: string
// eslint-disable-next-line prettier/prettier
): Promise<`0x${string}`> => {
  try {
    const { signer } = await getClientService()

    if (!signer) {
      throw new Error('Wallet signer not available')
    }

    // Set raw-hash not не plain message
    return await signMessage(signer, {
      account: address as `0x${string}`,
      message: { raw: messageHash as `0x${string}` }
    })
  } catch (error) {
    console.error('getHashSignature_err=', error)
    throw error
  }
}

