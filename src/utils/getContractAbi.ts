import axios from 'axios'
import { config } from 'globalConfigs'
import { retryFunction } from './retryFunction'

export const getContractAbiByApi = async (address: string) => {
  const callback = () => getContractAbi(address)

  try {
    return await retryFunction(callback)
  } catch (err) {
    console.log(err)
  }
}

export const getContractAbi = async (address: string) => {
  const params = {
    module: 'contract',
    action: 'getabi',
    address,
    apikey: config.bscscanApikey
  }

  const result = await axios.get(config.blockchain.urlEtherscanApi as string, { params })

  return JSON.parse(result.data.result)
}
