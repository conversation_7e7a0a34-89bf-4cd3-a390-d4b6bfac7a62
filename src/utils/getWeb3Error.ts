import { decodeAbiParameters } from 'viem'
import { errorMessages } from './toastrMessages'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import { EMessageCodes } from 'types/EMessageCodes'
import { TWords } from 'elements/SideBar/SideBar.config'

export const getWeb3Error = (errorCode: number | string): string => {
  switch (String(errorCode)) {
    case '0':
      return 'TRL_RELATION_ALREADY_EXIST'
    case '1':
      return 'LOCK_TIME_MUST_BE_LESS_7_DAYS'
    case '2':
      return 'NO_PERMITTION_TO_UNLOCK'
    case '3':
      return 'CONTRACT_LOCKED_UNTIL_7_DAYS'
    case '4':
      return 'NO_PERMITTION_TO_DELIVER'
    case '7':
      return 'PERMITION_ONLY_FOR_ADMINS'
    case '8':
      return 'DEFAULT_ADMIN_ROLE_CAN_BE_ONLY_TRANSFERRED'
    case '9':
      return 'DEFAULT_ADMIN_ROLE_CAN_NOT_BE_REVOKED_DIRECTLY'
    case '10':
      return 'DEFAULT_ADMIN_ROLE_CAN_NOT_BE_RENOUNCED_DIRECTLY'
    case '11':
      return 'TRANSFER_ROLE_TO_ZERO_ADDRESS'
    case '100':
      return 'CONFIG_MAX_WALLET_PERCENT_MUST_BE_MORE_0'
    case '101':
      return 'CONFIG_MAX_TX_MUST_BE_MORE_0'
    case '202':
      return 'TRL_AMOUNT_MUST_BE_MORE_0'
    case '203':
      return 'TRL_AMOUNT_EXCEEDS_MAXTXAMOUNT'
    case '204':
      return 'TRL_MAX_WALLET_AMOUNT_EXCEEDS'
    case '208':
      return 'TRL_INSUFFICIENT_BALANCE'
    case '300':
      return 'TASK_ALREADY_EXIST'
    case '302':
      return 'CURRENCY_MUST_BE_A_CONTRACT'
    case '303':
      return 'USER_TASK_SIGNATURE_IS_INVALID'
    case '304':
      return 'TASK_IS_NOT_SIGNED'
    case '305':
      return 'Proposal to self is not allowed'
    case '306':
      return 'RECIPIENT_MUST_BE_NOT_A_CONTRACT'
    case '307':
      return 'PROPOSAL_TO_CUSTOMER'
    case '308':
      return 'PROPOSAL_AMOUNT_MUST_BE_MORE_0'
    case '309':
      return 'PROPOSAL_NOT_EXIST'
    case '310':
      return 'YOU_NOT_A_PART_OF_PROPOSAL'
    case '311':
      return 'PROPOSAL_AMOUNT_NOT_EQL_SENDED_AMOUNT'
    case '312':
      return 'PROPOSAL_AMOUNT_NOT_EQL_CURRENCY_ALLOWANCE_AMOUNT'
    case '313':
      return 'PROPOSAL_ALREADY_SIGNED'
    case '314':
      return 'PROPOSAL_NOT_TO_YOU'
    case '315':
      return 'PROPOSAL_NOT_ACCEPTED_BY_CONTRACTOR'
    case '316':
      return 'TASK_CAN_BE_MARK_EXECUTED_ONLY_BY_CONTRACTOR'
    case '317':
      return 'TASK_CAN_BE_CANCELED_ONLY_BY_OWNERS'
    case '318':
      return 'TASK_CAN_BE_MARK_DONE_ONLY_BY_CUSTOMER'
    case '319':
      return 'TASK_STATUS_MUST_BE_INPROGRESS_OR_CANCEL_REQUEST'
    case '320':
      return 'TASK_STATUS_MUST_BE_INPROGRESS'
    case '321':
      return 'TASK_REFUND_PERCENTAGE_MUST_BE_EQUAL_100'
    case '322':
      return 'TASK_REFUND_PERCENT_MUST_BE_UPPER_0'
    case '323':
      return 'STOPPED_TASK_REFUND_ALLOWED_ONLY_TO_SUPPORT'
    case '324':
      return 'TASK_STATUS_MUST_BE_MARK_EXECUTED'
    case '325':
      return 'TASK_STATUS_MUST_BE_STOP_WITH_PROBLEM'
    case '326':
      return 'TASK_STATUS_MUST_BE_CREATED'
    case '327':
      return 'TASK_STATUS_MUST_BE_ACCEPTED'
    case '328':
      return 'INSUFFICIENT_BALANCE_OR_ALLOWANCE'
    case '329':
      return 'CANNOT_DEPOSIT_TRL_TOKENS'
    case '330':
      return 'TASK_CAN_BE_SET_ON_VOTING_BY_CUSTOMER_OR_CONTRACTOR'
    case '331':
      return 'TASK_CAN_BE_SET_ON_VOTING_ONLY_BY_ADMIN'
    case '332':
      return 'TASK_STATUS_MUST_BE_VOTING'
    case '333':
      return 'METHOD_CAN_BE_CALLED_BY_WINNER_OR_ADMIN'
    case '334':
      return 'TASK_VOTING_IS_ONGOING'
    case '335':
      return 'UNAVAILABLE_VOTING_CURRENCY'
    case '336':
      return 'TASK_STATUS_MUST_BE_VOTING_DONE'
    case '337':
      return 'VOTING_PARAMS_ALREADY_INITIALIZED'
    case '338':
      return 'MISSING_REQUIRED_ROLE'
    case '339':
      return 'VOTING_TIME_IS_OVER'
    case '340':
      return 'VOTING_TIME_IS_NOT_OVER'
    case '341':
      return 'BID_PRICE_TO_ZERO_ADDRESS'
    case '342':
      return 'BID_PRICE_FOR_TOKEN_IS_NOT_SET'
    case '343':
      return 'BID_INVALID_RECIPIENT'
    case '344':
      return 'BID_INVALID_TOKEN_AMOUNT'
    case '345':
      return 'METHOD_CAN_BE_CALLED_BY_PARTY_OR_ADMIN'
    case '400':
      return 'SRCN_INVALID_PEGGED_TOKEN_ADDRESS'
    case '401':
      return 'SRCN_INVALID_PEGGED_TOKEN_DECIMALS'
    case '402':
      return 'SRCN_DEPOSIT_AMOUNT_MUST_BE_MORE_0'
    case '403':
      return 'SRCN_RESULTING_AMOUNT_TOO_SMALL'
    case '404':
      return 'SRCN_PEGGED_TOKEN_TRANSFER_FAILED'
    case '405':
      return 'SRCN_WITHDRAW_AMOUNT_MUST_BE_MORE_0'
    case '406':
      return 'SRCN_INSUFFICIENT_BALANCE'
    case '407':
      return 'SRCN_RESULTING_PEGGED_TOKEN_AMOUNT_TOO_SMALL'
    case '408':
      return 'SRCN_INSUFFICIENT_PEGGED_TOKEN_BALANCE'
    case '800':
      return 'DEFAULT_CURRENCY_FEE_FOR_ETH'
    case '1000':
      return 'DEFAULT_CURRENCY_FEE'
    default:
      return ''
  }
}
const errorWeb3MessagesParsing = (error: any) => {
  // If error from JSON-RPC, return it as is
  if (error.data && error.message && error.code) {
    return error
  }

  if (error.response) {
    if (error.response.data.message && !Array.isArray(error.response.data.message)) {
      const res = Object.keys(errorMessages).find((item: string) =>
        error.response.data.message.match(item)
      )
      return res ? errorMessages[res] : error.response.data.message
    } else if (error.response.data.errorMessage) {
      const res = Object.keys(errorMessages).find((item: string) =>
        error.response.data.errorMessage.match(item)
      )
      return res ? errorMessages[res] : error.response.data.errorMessage
    }
  }

  return error
}

const sendToastrError = (words: TWords, first: string, second?: string) => {
  const reserve = second && words[second]
  const message = words[first] || (reserve || first)
  toastr(EToastrTypeMessage.ERROR, message)
}

export const handleWeb3Error = (error: any, words: TWords) => {
  const err: any = error
  const parsedErr = errorWeb3MessagesParsing(err)

  console.log('handleWeb3Error=', err, err && err.message)

  // Check if there is data in parsedErr
  const data = parsedErr.data || err.data

  if (data) {
    try {
      // eslint-disable-next-line prettier/prettier
      const raw = `0x${data.slice(10)}` as `0x${string}`
      // @ts-ignore
      const [errorCode] = decodeAbiParameters([{ type: 'string' }], raw) 

      const errFromContract = getWeb3Error(Number(errorCode))

      sendToastrError(words, errFromContract)
    } catch (decodeError) {
      sendToastrError(words, 'Failed to decode contract error')
    }
  } else {
    // If there's no data, try to extract code from message
    const errCode = err.message.match(/: (\d+):/)
    const errorCode = errCode ? errCode[1] : null
    if (errorCode) {
      const errFromContract = getWeb3Error(Number(errorCode))
      sendToastrError(words, errFromContract)
    } else {
      sendToastrError(words, err.message, EMessageCodes.FAILED_REQUEST)
    }
  }
}
