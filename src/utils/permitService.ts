/* eslint-disable prettier/prettier */
import { getContractAbi } from './getContractAbi'
import { getContractService } from 'wagmiContractService'
import { generatePrivateKey, privateKeyToAccount } from 'viem/accounts'
import { wagmiConfig } from 'elements/PrivatRoute/components/wagmiConfig'
import { getChainId, signTypedData, getBlock } from '@wagmi/core'
import { parseSignature } from 'viem'

export type GetSignedPermitParams = {
  token: `0x${string}`
  owner: `0x${string}`         
  spender: `0x${string}`
  value: bigint                
  deadline?: bigint   
  nameOverride?: string        // if name does not read
  version?: string             // domain version; default "1"
}

export type SignedPermit = {
  owner: `0x${string}`
  spender: `0x${string}`
  value: bigint
  nonce: bigint
  deadline: bigint
  v: bigint | undefined
  r: `0x${string}`
  s: `0x${string}`
  signature: `0x${string}`
}

const abi2612 = [
  { name: 'nonces', type: 'function', stateMutability: 'view', inputs:[{name:'owner',type:'address'}], outputs:[{type:'uint256'}]},
  { name: 'DOMAIN_SEPARATOR', type: 'function', stateMutability: 'view', inputs:[], outputs:[{type:'bytes32'}]},
  { name: 'permit', type: 'function', stateMutability: 'nonpayable',
    inputs:[
      {name:'owner',type:'address'},
      {name:'spender',type:'address'},
      {name:'value',type:'uint256'},
      {name:'deadline',type:'uint256'},
      {name:'v',type:'uint8'},
      {name:'r',type:'bytes32'},
      {name:'s',type:'bytes32'},
    ],
    outputs:[]
  },
] as const

function isPermitInAbi(abi: any[]): boolean {
  for (const f of abi) {
    if (f?.type !== 'function' || f?.name !== 'permit') continue
    const sig = (f.inputs ?? []).map((i: any) => i.type).join(',')
    if (sig === 'address,address,uint256,uint256,uint8,bytes32,bytes32') return true
  }
  return false
}

function looksLikeNoSelector(err: unknown): boolean {
  const msg = String((err as any)?.message ?? '')
  return (
    msg.includes('selector not recognized') ||
    msg.includes("Function selector was not recognized") ||
    msg.includes("function selector not recognized") ||
    msg.includes('missing revert data in call exception')
  )
}

export async function checkPermitFunctionality(token: string): Promise<boolean> {
  // 1) Quick check with abi if contract is verified
  try {
    const abi = await getContractAbi(token)
    if (isPermitInAbi(abi ?? [])) return true
  } catch {
    
  }

  // 2) Onchain checks: nonces + DOMAIN_SEPARATOR (или eip712Domain)
  const rand = privateKeyToAccount(generatePrivateKey())
  const svc = await getContractService(token)

  try {
    await svc.read('nonces', [rand.address])
  } catch {
    return false
  }

  let hasDomain = false
  try {
    await svc.read('DOMAIN_SEPARATOR', [])
    hasDomain = true
  } catch {
    try {
      await svc.read('eip712Domain', [])
      hasDomain = true
    } catch {}
  }
  if (!hasDomain) return false

  // 3) Cheking permit through simulateContract
  const owner = rand.address as `0x${string}`
  const spender = owner
  const value = BigInt(0)
  const deadline = BigInt(Math.floor(Date.now() / 1000) + 3600)
  const v = 27
  const r = `0x${'00'.repeat(32)}` as `0x${string}`
  const s = `0x${'00'.repeat(32)}` as `0x${string}`

  try {
    await (await getContractService(token, undefined, abi2612))
      .simulate('permit', [owner, spender, value, deadline, v, r, s])
    return true
  } catch (e) {
    if (looksLikeNoSelector(e)) return false // no method
    return true
  }
}

export async function getSignedPermit({
  token,
  owner,
  spender,
  value,
  deadline,
  nameOverride,
  version = '1',
}: GetSignedPermitParams): Promise<SignedPermit> {
  // 1) chainId
  const chainId = getChainId(wagmiConfig)

  // 2) name() и nonce
  let name = nameOverride
  const svc = await getContractService(token)
  try {
    name = name ?? await svc.read('name', [])
  } catch { }

  if (!name) {
    throw new Error('Contract name is not defined')
  }

    const nonce = await svc.read('nonces', [owner])
    
  // 3) deadline
  const block = await getBlock(wagmiConfig, { blockTag: 'latest' })
  const dl = deadline || block.timestamp + BigInt(60 * 60 * 24)

  // 4) EIP-712 typed data
  const domain = {
    name,
    chainId,
    verifyingContract: token,
    version
  } as const

  const types = {
    Permit: [
      { name: 'owner',   type: 'address'  },
      { name: 'spender', type: 'address'  },
      { name: 'value',   type: 'uint256'  },
      { name: 'nonce',   type: 'uint256'  },
      { name: 'deadline',type: 'uint256'  },
    ],
  } as const

  const message = {
    owner,
    spender,
    value,
    nonce,
    deadline: dl,
  } as const

  // 5) signature
  // @ts-ignore
  const signature = await signTypedData(wagmiConfig as any, {
    account: owner,
    types,
    primaryType: 'Permit',
    message,
    domain,
  }) as `0x${string}`

  const { r, s, v } = parseSignature(signature)

  return { owner, spender, value, nonce, deadline: dl, v, r, s, signature }
}