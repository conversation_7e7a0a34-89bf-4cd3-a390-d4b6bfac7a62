export const convertBigNumberToInteger = (
  bn: any | null | undefined,
  decimals: number = 0
): string => {
  if (!bn || bn.isZero()) return '0'

  try {
    const strValue = bn.toString()
    if (decimals <= 0) return strValue

    const padded = strValue.padStart(decimals + 1, '0')
    const integerPart = padded.slice(0, -decimals) || '0'
    return integerPart
  } catch (error) {
    console.error('Conversion error:', error)
    return '0'
  }
}

export const convertBigIntToInteger = (
  value: bigint | null | undefined,
  decimals: number = 0
): string => {
  if (!value || value === BigInt(0)) return '0'

  try {
    const strValue = value.toString()
    if (decimals <= 0) return strValue

    const padded = strValue.padStart(decimals + 1, '0')
    const integerPart = padded.slice(0, -decimals) || '0'
    return integerPart
  } catch (error) {
    console.error('Conversion error:', error)
    return '0'
  }
}
