import { useMemo } from 'react'
import { useSelector } from 'react-redux'
import { TSelectOption } from 'screens/client/EditProfile/EditProfile.model'
import { TState } from 'store'

interface IOption {
  id: number | string
  name: string
}

export const useNormalizeOptions = (rawOptions: IOption[]): TSelectOption[] | [] => {
  const words = useSelector((state: TState) => state.global.language.words)

  const options = useMemo(() => {
    if (rawOptions && rawOptions.length) {
      return rawOptions.map((item: IOption) => ({
        value: String(item.id),
        label: words[item.name]
      }))
    }
    return []
  }, [rawOptions, words])

  return options
}
