import { getContractService } from 'wagmiContractService'
import { EContractAddress } from 'globalConfigs'
import { API, api } from './api'

export const taskStatus = [
  'Created',
  'Accepted',
  'InProgress',
  'RequestCancelByClient',
  'RequestCancelByContractor',
  'Canceled',
  'StopedWithProblem',
  'CanceledBySupportCommentFromCustomer',
  'CanceledBySupportCommentFromContractor',
  'CanceledBySupportCommentFromBoth',
  'Executed',
  'Done',
  'Voting',
  'VotingDone',
  'CanceledByVoting'
]

export const deals = async (taskHash: string, options: { verbose?: boolean }) => {
  try {
    if (!EContractAddress.FDEscrowDiamond) {
      throw new Error('Missing FDEscrowDiamond')
    }

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDBidFacet
    )

    const rawDeal = await contract.read('deals', [taskHash])

    const parseProposals = (proposals: Array<Array<string | number>>) =>
      proposals.map(proposal => ({
        from: proposal[0],
        to: proposal[1],
        amount: proposal[2].toString(),
        contractorSignature: proposal[3],
        taskSignature: proposal[4]
      }))

    const parsedDeal = {
      taskHash: rawDeal[0],
      status: options.verbose ? taskStatus[Number(rawDeal[1])] : Number(rawDeal[1]),
      ethDeal: Boolean(rawDeal[2]),
      isRepeatableByCustomer: Boolean(rawDeal[3]),
      isRepeatableByContractor: Boolean(rawDeal[4]),
      currency: rawDeal[5],
      customer: rawDeal[6],
      contractor: rawDeal[7],
      proposals: rawDeal[8] instanceof Array ? parseProposals(rawDeal[8]) : [],
      chousedProposal: Number(rawDeal[9]),
      taskSignature: rawDeal[10],
      contractorSignature: rawDeal[11],
      refundCustomerPercent: Number(rawDeal[12]),
      refundContractorPercent: Number(rawDeal[13]),
      isConfirmedVotingByCustomer: Boolean(rawDeal[14]),
      isConfirmedVotingByContractor: Boolean(rawDeal[15]),
      votingRequestDeadline: options.verbose
        ? new Date(Number(rawDeal[16]) * 1000).toISOString()
        : Number(rawDeal[16]),
      votingDeadline: options.verbose
        ? new Date(Number(rawDeal[17]) * 1000).toISOString()
        : Number(rawDeal[17]),
      votingCurrency: rawDeal[18],
      customerVotes: Number(rawDeal[19]),
      contractorVotes: Number(rawDeal[20])
    }

    return parsedDeal
  } catch (error) {
    console.error('Error:', error)
    return null
  }
}

export const signSetTaskOnVoting = async (taskHash: string): Promise<string | null> => {
  try {
    const { data } = await api.post(API.SIGN_SET_TASK_ON_VOTING, { taskHash })
    return data as string
  } catch (error) {
    console.log('signSetTaskOnVoting error:', error)
    return null
  }
}
