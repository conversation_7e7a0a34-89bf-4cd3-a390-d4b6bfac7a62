import { Dispatch } from 'redux'

import { getHashSignature } from 'utils/getHashSignature'
import { EContractAddress, address0 } from 'globalConfigs'
import { api, API } from 'services/api'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import { isTaskInTransaction, setOpenInfoModal } from './web3.action'
import { EDealStatus, IDeal } from './web3.model'
import {
  agreeProposalSuccess,
  changeTaskStatus,
  checkTaskCancellationInfo,
  showCancelModal
} from '../Subcontract/Subcontract.actions'
import { ETaskStatus } from '../Subcontract/components/CreateTask/CreateTask.model'
import { ETransactionMessage } from 'components/Spinner/types'
import { TState } from 'store'
import {
  ICandidat,
  ITaskDataForRequest,
  ITaskRequestFormValues
} from '../TaskRequest/TaskRequest.model'
import {
  ICreateTaskCancellation,
  ITaskInfo,
  ITaskInfoTaskCancellation
} from '../Subcontract/Subcontract.model'
import { createTaskRequest } from '../TaskRequest/TaskRequest.thunk'
import {
  ECommentAuthorType,
  ETaskResponseStatus
} from '../Subcontract/components/Response/libs/constants/constants'
import { setShowSuccessRequestModal } from '../TaskRequest/TaskRequest.action'
import { getProposalThunk } from '../TaskPayment/TaskPayment.thunk'
import { TCancelTaskModal } from '../Subcontract/components/Task/Task.model'
import {
  createTaskCancellation,
  getTask,
  getTasks,
  updateTaskCancellation
} from '../Subcontract/Subcontract.thunk'
import { signSetTaskOnVoting } from 'services/contracts-api'
import { getDealThunk } from '../Deal/deal.thunk'
import { TVoice } from '../VotingList/VotingList.model'
import { delay } from 'utils/delay'
import { getVoting } from '../VotingList/VotingList.thunk'
import { setIsNotPaidYet } from '../VotingList/VotingList.actions'
import { checkWalletAddressWagmi } from 'helpers/checkWalletAddress'
import { EMessageCodes } from 'types/EMessageCodes'
import { handleWeb3Error } from 'utils/getWeb3Error'
import { getContractService } from 'wagmiContractService'
// import { getClientService } from 'wagmiClientService'
import { getAccount, connect, waitForTransactionReceipt } from '@wagmi/core'
import { injected } from 'wagmi/connectors'
import { wagmiConfig } from 'elements/PrivatRoute/components/wagmiConfig'
import { recoverAddressFromSignature } from 'utils/recoverAddressFromSignature'
import { parseUnits } from 'viem'
import * as permit from 'utils/permitService'


export const checkWSEventListeners = async (): Promise<{
  taskEvents: boolean
}> => {
  try {
    const res = await api.get(`${API.URL}/blockchain/listener-status`)
    return res.data
  } catch (error) {
    console.error('WS check failed:', error)
    return { taskEvents: false }
  }
}

// export const checkMetamaskConnection = (accounts: string[], words: any): string => {
//   if (accounts.length === 0) {
//     toastr(
//       EToastrTypeMessage.ERROR,
//       words['user.srcnManagement.connectMetamask'] || 'Please connect to MetaMask'
//     )
//     return 'error'
//   }
//   return ''
// }

export const activateMetaMask = async () => {
  const account = getAccount(wagmiConfig)

  if (account.status !== 'connected') {
    try {
      await connect(
        wagmiConfig,
        { connector: injected() }
      )
    } catch (err) {
      console.error('Web 3 wallet connect error:', err)
      throw new Error('Web 3 wallet connection failed')
    }
  }
}

export const checkRequiredDataWeb3 = async (data: TState) => {
  const { address, isRightWallet } = await checkWalletAddressWagmi(data)
  if (!isRightWallet) throw new Error(EMessageCodes.ERR_NOT_RIGHT_WALLET_ADDRESS)

  const { taskEvents } = await checkWSEventListeners()
  if (!taskEvents) throw new Error(EMessageCodes.BLOCKCHAIN_CONNECTION_WARNING)

  return { address }
}

// Upgraded
export const acceptTaskThunk = (task: any, typeUpdate: 'singleTask' | 'taskList') => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words
  const { taskHash } = task

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    // eslint-disable-next-line prettier/prettier
    await contract.writeAndWait('acceptTask', [taskHash, signature], address as `0x${string}`)
   
    await delay(500)
    if (typeUpdate === 'singleTask') {
      dispatch(getTask(task.id))
    }
    if (typeUpdate === 'taskList') {
      dispatch(
        changeTaskStatus({
          id: task.id,
          statusId: ETaskStatus.COMPLETED,
          statusName: 'TASK_STATUS_3',
          dealStatus: EDealStatus.Done,
          finishedAt: new Date().toISOString()
        })
      )
    }
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const signTaskThunk = (task: any, history: any) => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words
  const { price, taskHash } = task
  const taskPrice = Number(price) || 0

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    const { data } = await api.get(`${API.URL}/tokens/${task.currency}`)
    const taskCurrency = data.address || address0

    const txHash = await contract.write(
      'signTask',
      [taskHash, taskCurrency, taskPrice, address0, signature, task.isRepeatable],
      // eslint-disable-next-line prettier/prettier
      address as `0x${string}`
    )

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    const receipt = await waitForTransactionReceipt(wagmiConfig, { hash: txHash })

    if(receipt.status !== 'success') {
      throw new Error(EMessageCodes.ERR_TRANSACTION_FAILED)
    }

    await delay(500)
    history.push('/dashboard/subcontract?page=1&task=all-tasks')
    dispatch(getTasks(10, 1, 'tasks', '', false, false))
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])
  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const transferTokensToContractThunk = (task: any, history: any) => async (
  dispatch: Dispatch,
  getData: () => TState
) => {
  const words = getData().global.language.words
  const { taskHash } = task

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const deal = (await dispatch(getDealThunk(taskHash) as any)) as IDeal
    if (!deal) throw new Error(words['user.subcontract.voting.dealNotFound'] || 'Deal not found')

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDFundsFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    const taskAmount = BigInt(deal.proposals[deal.chousedProposal as any].amount)


    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    const isPermit = deal.ethDeal ? false : await permit.checkPermitFunctionality(deal.currency)



    if (deal.ethDeal) {
      await contract.writeAndWait('deposit', [taskHash, signature], address as `0x${string}`, taskAmount)
    } else if (!isPermit) {
      await contract.writeAndWait('approve', [EContractAddress.FDEscrowDiamond, taskAmount], address as `0x${string}`)
       await contract.writeAndWait('deposit', [taskHash, signature], address as `0x${string}`)
    } else {
      const { v, r, s, deadline } = await permit.getSignedPermit({
        token: deal.currency as `0x${string}`,
        owner: address as `0x${string}`,
        spender: EContractAddress.FDEscrowDiamond,
        value: taskAmount,
      })
      await contract.writeAndWait('depositWithPermit', [taskHash, signature, deadline, v, r, s])
    }

    await delay(500)
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])
    history.push('/dashboard/subcontract?task=all-tasks&page=1&fromPrice=')

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}


// Upgraded
export const acceptProposalThunk = (task: any, proposal: any) => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words
  const { taskHash } = task
  // const amount = proposal.budget

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contractorAddress = recoverAddressFromSignature(proposal.makeProposalSignature, taskHash)


    const deal = (await dispatch(getDealThunk(taskHash) as any)) as IDeal
    if (!deal) throw new Error(words['user.subcontract.voting.dealNotFound'] || 'Deal not found')

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )

    const proposalIdx = deal.proposals.findIndex(
      currentProposal =>
        currentProposal.from.toLowerCase() === contractorAddress.toLowerCase() ||
        currentProposal.to.toLowerCase() === contractorAddress.toLowerCase()
    )
    console.log('ProposalIdx: ', proposalIdx)

    // TODO:  Change error name
    if(proposalIdx === -1) {
      throw new Error(words['user.subcontract.voting.proposalNotFound'] || 'Proposal not found')
    }

    // const { data } = await api.get(`${API.URL}/tokens/${task.currency}`)
    // const taskCurrency = data.address || address0

    const signature = await getHashSignature(address as `0x${string}`, taskHash)
      
      
      const txHash = await contract.write('acceptProposal', [proposalIdx, taskHash, signature], address as `0x${string}`)

      toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

      const receipt = await waitForTransactionReceipt(wagmiConfig, { hash: txHash })
      
      if(receipt.status !== 'success') {
        throw new Error(EMessageCodes.ERR_TRANSACTION_FAILED)
      }

      await delay(500)
      await dispatch(getProposalThunk(proposal.id))
      toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

    // if (taskCurrency === address0) {
    //   const signature = await getHashSignature(address as `0x${string}`, taskHash)
      
    //   dispatch(isTaskInTransaction(true))
      
    //   const txHash = await contract.write('acceptProposal', [proposalIdx, taskHash, signature], address as `0x${string}`)

    //   toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    //   const receipt = await waitForTransactionReceipt(wagmiConfig, { hash: txHash })
      
    //   if(receipt.status !== 'success') {
    //     throw new Error(EMessageCodes.ERR_TRANSACTION_FAILED)
    //   }

    //   await delay(500)
    //   await dispatch(getProposalThunk(proposal.id))
    //   toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

    // } else {
      
    //   await delay(1000) // TODO: delete when etherscan v2 is added
    //   const token = await getContractService(taskCurrency)
    //   const decimals = await token.read('decimals',[])
      
    //   const INITIAL_INT = parseUnits('1000000', Number(decimals))
    //   console.log('INITIAL_INT: ', INITIAL_INT)
    //   const allowance = await token.read('allowance', [address, EContractAddress.FDEscrowDiamond])
    //   console.log('Allowance: ', allowance)
      
    //   if (allowance < amount && amount < INITIAL_INT) {
    //     // FIXME: Wrong implementation of permit 
    //     // const deadline = Math.floor(Date.now() / 1000) + 3600 * 24 * 365 // one year
    //     // const nonce = await token.methods.nonces(address).call()
        
    //     // await permit(
    //     //   token,
    //     //   address,
    //     //   EContractAddress.FDEscrowDiamond,
    //     //   deadline,
    //     //   nonce,
    //     //   gasPrice,
    //     //   INITIAL_INT,
    //     //   dispatch,
    //     //   words
    //     // )

    //     // TODO: Replace approve method with permit in transferToken ?
    //     console.log('Approving allowance...')
    //     const hash = await token.writeAndWait('approve', [EContractAddress.FDEscrowDiamond, INITIAL_INT], address as `0x${string}`)
    //     console.log('Approval hash: ', hash)
    //   }

      
    //   const signature = await getHashSignature(address as string, taskHash)
      
    //   dispatch(isTaskInTransaction(true))

    //   await contract.writeAndWait('acceptProposal', [proposalIdx, taskHash, signature], address as `0x${string}`)
    
    //   await dispatch(getProposalThunk(proposal.id))
    //   toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])
    // }
  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const handleMakeProposalThunk = (
  formValues: ITaskRequestFormValues,
  { id, taskHash }: Pick<ITaskDataForRequest, 'id' | 'taskHash'>,
  isOfferPage: boolean, // if true - reverse flow
  candidat: ICandidat
) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())
    console.log('IsOfferPage: ', isOfferPage)
    const body = {
      ...formValues,
      taskId: id,
      status: ETaskResponseStatus.DRAFT,
      authorId: isOfferPage ? candidat.id : undefined
    }

    const isDraftCreated = await dispatch(createTaskRequest(body))
    if (!isDraftCreated) {
      return toastr(EToastrTypeMessage.ERROR, words[EMessageCodes.FAILED_REQUEST])
    }

    // 2. Create proposal
    const deal = (await dispatch(getDealThunk(taskHash))) as IDeal
    if (!deal) throw new Error(words['user.subcontract.voting.dealNotFound'] || 'Deal not found')
    console.log('Deal customer: ', deal.customer)
    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    await delay(1000) //TODO: remove after migrating to etherscan v2
    const token = await getContractService(deal.currency)

    const decimals = await token.read('decimals', [])
    const budget = parseUnits(formValues.budget.toString(), Number(decimals))
    console.log('budget: ', budget)

    const dealCustomer = isOfferPage ? candidat.walletAddress : deal.customer

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])
    const txHash = await contract.write(
      'makeProposal',
      [taskHash, dealCustomer, budget, signature, formValues.isRepeatable],
      address as `0x${string}`
    )

    const receipt = await waitForTransactionReceipt(wagmiConfig, { hash: txHash })
    
    if(receipt.status !== 'success') {
      throw new Error(EMessageCodes.ERR_TRANSACTION_FAILED)
    }

    await delay(500)
    dispatch(setShowSuccessRequestModal(true))
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

  
  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    dispatch(isTaskInTransaction(false))
    console.error('handleMakeProposal_error=', error)
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Reverse flow 
// Upgraded...
export const agreeProposalThunk = (task: ITaskInfo) => async (
  dispatch: Dispatch,
  getData: () => TState
) => {
  const words = getData().global.language.words
  const { taskHash } = task

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const deal = (await dispatch(getDealThunk(taskHash) as any)) as IDeal
    if (!deal) throw new Error(words['user.subcontract.voting.dealNotFound'] || 'Deal not found')

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    const proposalIdx = deal.proposals.findIndex(
      currentProposal => currentProposal.to.toLowerCase() === (address as string).toLowerCase()
    )
    console.log('ProposalIdx: ', proposalIdx)

    // TODO:  Change error name
    if(proposalIdx === -1) {
      throw new Error(words['user.subcontract.voting.proposalNotFound'] || 'Proposal not found')
    }

    await contract.writeAndWait('acceptProposal', [proposalIdx, taskHash, signature], address as `0x${string}`)

    await delay(500)
    const response = await api.get(
      `${API.URL}${API.TASK_REQUEST}/${task.id}/find-by-task-by-user`
    )
    const data = response.data
    const payload = {
      taskId: task.id,
      responsesId: data.id,
      makeProposalSignature: data.makeProposalSignature
    }

        dispatch(agreeProposalSuccess(payload))
        toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

  } catch (error) {
     toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const cancelTaskThunk = (
  values: any,
  type: TCancelTaskModal,
  task: any,
  currentUserId: number,
  isCustomer: boolean,
  isTaskCancellation: ITaskInfoTaskCancellation
) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words
  const { taskHash } = task

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)
    if (!signature) return

    // 1. Create draft in DB
    if (type === 'cancellation') {
      const taskCancellation: Partial<ICreateTaskCancellation> = {
        taskId: task.id,
        comment: values.cancelDescription,
        authorId: currentUserId,
        refundCustomerPercent: 100,
        refundContractorPercent: 0,
        isDraft: true,
        authorType: isCustomer ? ECommentAuthorType.CUSTOMER : ECommentAuthorType.CONTRACTOR
      }

      let isDraftCreated = null
      if (isTaskCancellation) {
        isDraftCreated = await dispatch(
          updateTaskCancellation(taskCancellation, isTaskCancellation.id)
        )
      } else {
        isDraftCreated = await dispatch(createTaskCancellation(taskCancellation))
      }

      if (!isDraftCreated) {
        toastr(EToastrTypeMessage.ERROR, words[EMessageCodes.FAILED_REQUEST])
        return
      }
    }

    // 2. Contract handle
    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('cancelTask', [taskHash, signature], address as `0x${string}`)
    
    await delay(500)
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])
    dispatch(getTask(task.id))
    dispatch(getDealThunk(taskHash))
    dispatch(showCancelModal(''))
    dispatch(checkTaskCancellationInfo(null))
    setOpenInfoModal(true)
  } catch (error) {
    console.log('cancelTask_onError=', error)
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const setTaskOnVotingThunk = (task: any) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words
  const { taskHash } = task

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDVotingFacet
    )
    const signature = await signSetTaskOnVoting(taskHash)


    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('setTaskOnVoting', [taskHash, signature], address as `0x${string}`)


    await dispatch(getDealThunk(taskHash))
    const rawDeal = getData().client.deal
    const deal = rawDeal && rawDeal.data

    if (deal) {
      toastr(EToastrTypeMessage.SUCCESS, 'Task has been set on voting')
    } else {
      throw new Error(words['user.subcontract.voting.dealNotFound'])
    }

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const selectTaskVotingSideThunk = (
  voteFor: TVoice,
  taskHash: string,
  votingId: number
) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words
  const isCustomerSide = voteFor === 'customer'

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDVotingFacet
    )

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('selectTaskVotingSide', [taskHash, isCustomerSide], address as `0x${string}`)


    await dispatch(getDealThunk(taskHash))
    const rawDeal = getData().client.deal
    const deal = rawDeal && rawDeal.data

    if (deal) {
      await delay(500)
      const res = await dispatch(getVoting(votingId))
      if (res === 'ok') {
        toastr(EToastrTypeMessage.SUCCESS, `The ${voteFor} has received the vote`)
      }
    } else {
      throw new Error(words['user.subcontract.voting.dealNotFound'])
    }
  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Needs testing
export const endVotingAndCountVotesThunk = (taskHash: string, votingId: number) => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const contract  = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDVotingFacet
    )
    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('resolveVoting', [taskHash], address as `0x${string}`)

    await delay(500)
    dispatch(getVoting(votingId))
    dispatch(setIsNotPaidYet(true))
    dispatch(getDealThunk(taskHash))
    toastr(
      EToastrTypeMessage.SUCCESS,
      words['user.subcontract.voteCountingCompleted'] || 'Vote counting completed'
    )

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Needs testing
export const getPaymentByVotingThunk = (taskHash: string) => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words

  try {
    dispatch(isTaskInTransaction(true))

    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDVotingFacet
    )

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('getPaymentByVoting', [taskHash], address as `0x${string}`)

    toastr(EToastrTypeMessage.SUCCESS, words['user.subcontract.voting.refundSuccess'])
    dispatch(setIsNotPaidYet(false))

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Upgraded
export const handleStopTaskThunk = (
  values: any,
  task: any,
  currentUserId: number,
  isCustomer: boolean,
  numInRange: number,
  isOnVoting: boolean,
  isTaskCancellation: ITaskInfoTaskCancellation,
  createVoting?: (endOfVoting: Date, taskId: number) => (dispatch: Dispatch) => Promise<void>
) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words
  const { taskHash } = task

  if (!task.executor) {
    toastr(EToastrTypeMessage.ERROR, `Task executor is not defined. Assign executor to the task`)
    return
  }

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)

    // 1. Create draft in DB
    const taskCancellation: Partial<ICreateTaskCancellation> = {
      taskId: task.id,
      comment: values.cancelDescription,
      authorId: currentUserId,
      isDraft: true,
      authorType: isCustomer ? ECommentAuthorType.CUSTOMER : ECommentAuthorType.CONTRACTOR,
      [isCustomer ? 'refundCustomerPercent' : 'refundContractorPercent']: numInRange
    }

    let isDraftCreated

    if (isTaskCancellation) {
      isDraftCreated = await dispatch(
        updateTaskCancellation(taskCancellation, isTaskCancellation.id)
      )
    } else {
      isDraftCreated = await dispatch(createTaskCancellation(taskCancellation))
    }

    if (!isDraftCreated) {
      return toastr(EToastrTypeMessage.ERROR, words[EMessageCodes.FAILED_REQUEST])
    }

    // 2. Handle contaract
    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait(
      'stopTask',
      [taskHash, signature, numInRange],
      address as `0x${string}`
    )
   
    await delay(500)
    if (task.taskCancellation && task.taskCancellation.publicVoting) {
      toastr('success', 'Comment successfully added')
    } else {
      if (isOnVoting && createVoting) {
        await dispatch(setTaskOnVotingThunk(task))
      }
    }
    dispatch(checkTaskCancellationInfo(null))
    dispatch(getTask(task.id))
    dispatch(getDealThunk(taskHash))
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

    } catch (error) {
    console.log('error=', error)
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Needs testing
export const stopTaskWithProblemByAdminThunk = (
  values: any,
  task: any,
  numInRange: number,
  currentUserId: number
) => async (dispatch: any, getData: () => TState) => {
  const words = getData().global.language.words
  const { taskHash } = task
  
  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )

    const refundCustomerPercent = numInRange
    const refundContractorPercent = 100 - numInRange

    // 1. Create draft in DB
    const taskCancellation: Partial<ICreateTaskCancellation> = {
      taskId: task.id,
      comment: values.cancelDescription,
      authorId: currentUserId,
      isDraft: true,
      authorType: ECommentAuthorType.SUPPORT
    }

    const isDraftCreated = await dispatch(
      updateTaskCancellation(taskCancellation, task.taskCancellation.id)
    )
    if (!isDraftCreated) {
      return toastr(EToastrTypeMessage.ERROR, words[EMessageCodes.FAILED_REQUEST])
    }

    // 2. Handle contaract

    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('processStoppedTask', [taskHash, refundCustomerPercent, refundContractorPercent], address as `0x${string}`)

    
    await dispatch(getDealThunk(taskHash))
    await delay(500)
    dispatch(getTask(task.id))
    dispatch(getDealThunk(taskHash))
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])

  } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}

// Update
export const markExecutedThunk = (taskHash: string, history: any) => async (
  dispatch: any,
  getData: () => TState
) => {
  const words = getData().global.language.words

  try {
    dispatch(isTaskInTransaction(true))
    const { address } = await checkRequiredDataWeb3(getData())

    const contract = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDTaskFacet
    )
    const signature = await getHashSignature(address as string, taskHash)
    
    toastr(EToastrTypeMessage.INFO, words[`${ETransactionMessage.TRANSACTION_MESSAGE}`])

    await contract.writeAndWait('markExecuted', [taskHash, signature], address as `0x${string}`)

    await delay(500)
    toastr(EToastrTypeMessage.SUCCESS, words[`${ETransactionMessage.TRANSACTION_SUCCESS}`])
    history.push(`/dashboard/subcontract?task=all-tasks&page=1&fromPrice=`)

    } catch (error) {
    toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
    handleWeb3Error(error, words)
  } finally {
    dispatch(isTaskInTransaction(false))
  }
}
