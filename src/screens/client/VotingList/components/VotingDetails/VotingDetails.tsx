import React, { FC, useEffect, useMemo, useState } from 'react'
import BackButton from 'components/BackButton/BackButton'
import cls from 'classnames'
import styles from './VotingDetails.module.scss'
import { VotingStatusBar } from './components/VotingStatusBar'
import FormControl from '@mui/material/FormControl'
import FormControlLabel from '@mui/material/FormControlLabel'
import Radio from '@mui/material/Radio'
import RadioGroup from '@mui/material/RadioGroup'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { TVotingDetailsProps } from './VotingDetails.model'
import { categoryOptions } from 'components/Subcontract/SubcontractFilter/SubcontractFilter'
import { countPercent } from './libs/helpers/countPercent'
import { VotingModal } from '../VotingModal/VotingModal'
import { TComment, TCommentType } from '../VotingModal/VotingModal.model'
import { UserAvatar } from 'components/UserAvatar'
import { useParams } from 'react-router'
import { TVoice, TVoting } from '../../VotingList.model'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import { Button } from 'components/UiKit/Button'
import { EDealStatus } from 'screens/client/Web3/web3.model'
import dayjs from 'utils/dayjs'
import CircularLoader from 'components/CircularLoader/CircularLoader'
import { ECommentAuthorType } from 'screens/client/Subcontract/components/Response/libs/constants/constants'
import { TaskDeal } from 'screens/client/Deal/deal.model'
import { useTimer } from 'hooks/useTimer'
import { VotingCommentBlock } from './components/VotingCommentBlock/VotingCommentBlock'
import { getClientService } from 'wagmiClientService'

const VotingDetailsComponent: FC<TVotingDetailsProps> = ({
  deal,
  voting,
  currentUser,
  isNotPaidYet,
  getVoting,
  updateVotingDate,
  setTaskOnVotingThunk,
  selectTaskVotingSideThunk,
  setIsNotPaidYet,
  getPaymentByVotingThunk,
  getVotingSuccess,
  clearDeal,
  getDealThunk,
  endVotingAndCountVotesThunk
}) => {
  const words = useSelector((state: TState) => state.global.language.words)

  const { id } = useParams<{ id: string }>()
  const { min, start, pause } = useTimer(30000, false)

  const [isShowModal, setIsShowModal] = useState(false)
  const [modalData, setModalData] = useState<TComment>()
  const [isCurrentUserWinner, setIsCurrentUserWinner] = useState<boolean | null>(null)
  const [isCurrentUserCustomerOrContractor, setIsCurrentUserCustomerOrContractor] = useState<
    boolean | null
  >(null)

  const { customerPercent, executorPercent } = useMemo(() => {
    const obj = {
      customerPercent: 0,
      executorPercent: 0
    }
    if (voting) {
      obj.customerPercent = countPercent(
        Number(voting.customerVoices),
        Number(voting.executorVoices)
      )
      obj.executorPercent = countPercent(
        Number(voting.executorVoices),
        Number(voting.customerVoices)
      )
    }

    return obj
  }, [voting])

  const { dealStatus, isVotingDone, isExpiredDate } = useMemo(() => {
    const obj: any = {
      dealStatus: null,
      isExpiredDate: false,
      isVotingDone: false
    }

    if (deal) {
      const statusDeal = +deal.status
      obj.dealStatus = statusDeal
      const isExpDate =
        Number(deal.votingDeadline) === 0
          ? false
          : new Date(Number(deal.votingDeadline) * 1000) < new Date()
      obj.isExpiredD = isExpDate

      if (statusDeal !== EDealStatus.StopedWithProblem && isExpDate) {
        obj.isVotingDone = true
      }
    }

    return obj
  }, [deal, min])

  useEffect(() => {
    if (deal) {
      if (!isExpiredDate) {
        start()
        return
      }

      pause()
    }
  }, [isExpiredDate, min, deal])

  const { commentFromCustomer, commentFromContractor } = useMemo(() => {
    let contractorLastComment = ''
    let customerLastComment = ''

    if (
      voting &&
      voting.task &&
      voting.task.taskCancellation &&
      voting.task.taskCancellation.comments
    ) {
      const latestByType = voting.task.taskCancellation.comments.reduce((acc: any, item) => {
        const currentDate = new Date(item.updatedAt)

        // Если тип ещё не встречался или текущая дата новее
        if (!acc[item.authorType] || currentDate > new Date(acc[item.authorType].createdAt)) {
          acc[item.authorType] = item
        }

        return acc
      }, {})

      if (latestByType[ECommentAuthorType.CUSTOMER]) {
        customerLastComment = latestByType[ECommentAuthorType.CUSTOMER].comment
      }
      if (latestByType[ECommentAuthorType.CONTRACTOR]) {
        contractorLastComment = latestByType[ECommentAuthorType.CONTRACTOR].comment
      }
    }

    return {
      commentFromCustomer: customerLastComment,
      commentFromContractor: contractorLastComment
    }
  }, [voting])

  const currentUserSide = () => {
    if (voting) {
      switch (currentUser.id) {
        case voting.task.customer.id:
          return 'customer'

        case voting.task.executor.id:
          return 'executor'

        default:
          return 'participant'
      }
    }

    return null
  }

  useEffect(() => {
    return () => {
      clearDeal()
      getVotingSuccess(null)
    }
  }, [])

  const isVotingConfirmedByBothSides = () => {
    let confirmed = false
    if (deal) {
      const { isConfirmedVotingByContractor, isConfirmedVotingByCustomer } = deal
      if (isConfirmedVotingByContractor && isConfirmedVotingByCustomer) {
        confirmed = true
      }
      if (dealStatus === EDealStatus.Voting) {
        confirmed = true
      }
    }
    return confirmed
  }

  const isCurrentUserConfirmedVoting = (dealProp: TaskDeal) => {
    if (
      dealStatus === EDealStatus.Voting ||
      dealStatus === EDealStatus.VotingDone ||
      dealStatus === EDealStatus.CanceledByVoting
    ) {
      return true
    }

    if (!voting) return true

    const isExpiredRequestDate =
      new Date(Number(dealProp.votingRequestDeadline) * 1000) < new Date()
    const isInitiator = voting.task.cancelerId === currentUser.id

    if (!isExpiredRequestDate && isInitiator) {
      return true
    }
    if (isExpiredRequestDate && isInitiator) {
      return false
    }

    const confirmations = {
      customer: dealProp.isConfirmedVotingByCustomer,
      executor: dealProp.isConfirmedVotingByContractor
    }
    const userType = currentUserSide()
    return userType === 'customer' || userType === 'executor' ? confirmations[userType] : null
  }

  useEffect(() => {
    const checkUserSide = async () => {
      if (deal) {
        const { address: currentUserAddress } = await getClientService()
        const { customer, contractor } = deal
        setIsCurrentUserCustomerOrContractor(
          currentUserAddress === customer || currentUserAddress === contractor
        )
      }
    }

    if (deal && voting && Number(deal.votingDeadline) === 0) {
      const votingDeadline = Number(deal.votingRequestDeadline) * 1000
      updateVotingDate(new Date(votingDeadline), voting.id)
    }

    if (deal && voting && Number(deal.votingDeadline) !== 0) {
      const votingDeadline = new Date(Number(deal.votingDeadline) * 1000)
      updateVotingDate(new Date(votingDeadline), voting.id)
    }

    if (isCurrentUserCustomerOrContractor === null) {
      checkUserSide()
    }
  }, [deal])

  useEffect(() => {
    if (voting && voting.task) {
      if (voting.task.taskHash && !deal) {
        getDealThunk(voting.task.taskHash)
      }

      if (isCurrentUserCustomerOrContractor) {
        setIsNotPaidYet(voting.task.dealStatus !== EDealStatus.CanceledByVoting)

        const { customerVoices, executorVoices } = voting

        // if there is draw customer always wins (according to the contract logic)
        if (customerVoices === executorVoices && currentUserSide() === 'customer') {
          setIsCurrentUserWinner(true)
          return
        }

        const winnerSide = executorVoices > customerVoices ? 'executor' : 'customer'

        setIsCurrentUserWinner(currentUserSide() === winnerSide)
      }
    }
  }, [voting, deal])

  useEffect(() => {
    if (id) {
      getVoting(+id)
    }
    selectComment('customer')
  }, [id])

  const endVoting = (vot: TVoting) => {
    if (vot.task && vot.task.taskHash) {
      endVotingAndCountVotesThunk(vot.task.taskHash, +id)
    } else {
      toastr(EToastrTypeMessage.ERROR, words['admin.vacancies.taskHashNotDefined'])
    }
  }

  const getPayment = (vot: TVoting) => {
    if (vot.task && vot.task.taskHash) {
      getPaymentByVotingThunk(vot.task.taskHash)
    } else {
      toastr(EToastrTypeMessage.ERROR, words['admin.vacancies.taskHashNotDefined'])
    }
  }

  const handleChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (voting && voting.task && voting.task.taskHash) {
      selectTaskVotingSideThunk(event.target.value as TVoice, voting.task.taskHash, +id)
    } else {
      toastr(EToastrTypeMessage.ERROR, 'taskHash is not defined')
    }
  }

  const selectComment = (type: TCommentType) => {
    if (!voting) {
      return
    }

    const { taskCancellation, customer, executor } = voting.task

    if (taskCancellation && customer && executor) {
      setModalData({
        type,
        desc: type === 'customer' ? commentFromCustomer : commentFromContractor,
        name: type === 'customer' ? customer.fullName : executor.fullName,
        avatar: type === 'customer' ? customer.photo : executor.photo,
        percent:
          type === 'customer'
            ? taskCancellation.refundCustomerPercent
            : taskCancellation.refundContractorPercent
      })
    }
  }

  const openModal = () => {
    setIsShowModal(true)
  }

  const closeModal = () => {
    setIsShowModal(false)
  }

  const customerCommentData = useMemo(() => {
    if (!voting) return null

    const customer = commentFromCustomer ? voting.task.taskCancellation.refundCustomerPercent : 100
    const performer = 100 - customer

    return {
      photo: voting.task.customer.photo,
      fullName: voting.task.customer.fullName,
      comment: commentFromCustomer,
      refundPercent: { customer, performer }
    }
  }, [voting, commentFromCustomer])

  const contractorCommentData = useMemo(() => {
    if (!voting) return null

    const performer = voting.task.taskCancellation.refundContractorPercent
    const customer = 100 - performer

    return {
      photo: voting.task.executor.photo,
      fullName: voting.task.executor.fullName,
      comment: commentFromContractor,
      refundPercent: { customer, performer }
    }
  }, [voting, commentFromCustomer])

  const commentBlockOnClick = (type: 'customer' | 'executor') => () => {
    selectComment(type)
    openModal()
  }

  const handleShowVotingTime = () => {
    if (deal && voting) {
      if (Number(deal.votingDeadline) === 0) {
        return dayjs(Number(deal.votingRequestDeadline) * 1000).format(`DD.MM.YYYY | HH:mm`)
      }

      return dayjs(deal && Number(deal.votingDeadline) * 1000).format(`DD.MM.YYYY | HH:mm`)
    }
    return '-'
  }

  return (
    <div className={styles['container']}>
      <div className={styles['header']}>
        <h1>{words['user.voting.jobCenter']}</h1>
      </div>
      <div className={styles.backbtn}>
        <BackButton />
      </div>

      {voting && voting.id === +id && (
        <>
          <div className={styles.content}>
            <div className={cls(styles['desc'], styles['block'])}>
              <div className={styles['desc-body']}>
                <div>{voting.task.title}</div>
              </div>

              <div className={styles['desc-footer']}>
                <div className={styles['category-wrapper']}>
                  <div className={styles['category']}>
                    <div className={styles['category-label']}>
                      {words['user.subcontract.header.taskType']}:
                    </div>
                    <div className={styles['value']}>
                      {voting.task.type && categoryOptions[+voting.task.type - 1].label}
                    </div>
                  </div>

                  {/* Deadline */}
                  <div className={styles['category']}>
                    <div className={styles['category-label']}>
                      {deal && Number(deal.votingDeadline) === 0
                        ? words['user.voting.votingRequestDeadline'] || 'Voting request deadline'
                        : words['user.voting.endOfVoting']}
                      :
                    </div>
                    <div className={cls(styles['value'], { [styles['value-red']]: isVotingDone })}>
                      {deal && voting ? handleShowVotingTime() : <CircularLoader />}
                    </div>
                  </div>

                  <div className={styles.category}>
                    <div className={styles['category-label']}>
                      {words['user.subcontract.task.budget']}:
                    </div>
                    <div className={cls(styles.value, styles['value-price'])}>
                      {`${String(voting.task.price)} ${voting.task.token.symbol}`}
                    </div>
                  </div>
                </div>

                {/* Buttons */}
                {dealStatus === EDealStatus.Voting &&
                  isVotingDone &&
                  isCurrentUserCustomerOrContractor && (
                    <div className={cls(styles['category'], styles['category-row'])}>
                      <div />
                      <Button
                        onClick={() => endVoting(voting)}
                        className={cls(styles['button'], styles['button-content'])}
                      >
                        <span>{words['user.subcontract.countVotes'] || 'Count votes'}</span>
                      </Button>
                    </div>
                  )}

                {isVotingDone && dealStatus === EDealStatus.VotingDone && isNotPaidYet && (
                  <div className={cls(styles['category'], styles['category-row'])}>
                    <span
                      className={cls({
                        [styles['value-green']]: isCurrentUserWinner,
                        [styles['value-red']]: !isCurrentUserWinner
                      })}
                    >
                      {isCurrentUserWinner
                        ? words['user.voting.youWonVote']
                        : words['user.subcontract.youLostVote'] || 'You lost the vote'}
                    </span>
                    {isCurrentUserWinner && (
                      <Button
                        onClick={() => getPayment(voting)}
                        className={cls(styles['button'], styles['button-content'])}
                      >
                        <span>{words['user.subcontract.getPayment'] || 'Get Payment'}</span>
                      </Button>
                    )}
                  </div>
                )}

                {deal &&
                  !isVotingDone &&
                  !isCurrentUserConfirmedVoting(deal) &&
                  isCurrentUserCustomerOrContractor && (
                    <div className={cls(styles['category'], styles['category-row'])}>
                      <span className={styles['value-red']}>
                        {words['user.voting.confirmVotingFirst']}
                      </span>
                      <Button
                        onClick={() => setTaskOnVotingThunk(voting.task)}
                        className={cls(styles['button'], styles['button-content'])}
                        disabled={!(voting && voting.task.taskHash)}
                      >
                        <span>{words['user.voting.confirmVoting']}</span>
                      </Button>
                    </div>
                  )}
              </div>
            </div>

            <div className={styles['votingWrapper']}>
              <div className={styles['comments']}>
                {customerCommentData && (
                  <VotingCommentBlock
                    type="customer"
                    data={customerCommentData}
                    onClick={
                      customerCommentData.comment ? commentBlockOnClick('customer') : undefined
                    }
                  />
                )}

                {contractorCommentData && (
                  <VotingCommentBlock
                    type="performer"
                    data={contractorCommentData}
                    onClick={commentBlockOnClick('executor')}
                  />
                )}
              </div>

              <div className={cls(styles['block'], styles['voting'])}>
                <div className={styles['header']}>
                  <h2 className={styles['subtitle']}>{words['user.voting.voting']}</h2>
                </div>
                <div className={styles['percents']}>
                  {voting.isVoted || isVotingDone ? (
                    <>
                      <div className={styles['customer']}>
                        <div className={styles['avatar-wrapper']}>
                          <UserAvatar
                            className={styles['avatar']}
                            photo={voting.task.customer.photo}
                          />
                          <div className={styles['name']}>{voting.task.customer.fullName}</div>
                        </div>
                        <div className={styles['value']}>{customerPercent}%</div>
                        <VotingStatusBar color="red" percent={customerPercent} />
                      </div>
                      <div className={styles['executor']}>
                        <div className={styles['avatar-wrapper']}>
                          <UserAvatar
                            className={styles['avatar']}
                            photo={voting.task.executor ? voting.task.executor.photo : null}
                          />
                          <div className={styles['name']}>
                            {voting.task.executor ? voting.task.executor.fullName : ''}
                          </div>
                        </div>
                        <div className={styles['value']}>{executorPercent}%</div>
                        <VotingStatusBar color="green" percent={executorPercent} />
                      </div>
                    </>
                  ) : isVotingConfirmedByBothSides() && currentUser.walletAddress ? (
                    <FormControl>
                      <RadioGroup
                        name="voting"
                        className={styles['radio-group']}
                        onChange={handleChange}
                      >
                        <FormControlLabel
                          value="customer"
                          className={styles['radio-label']}
                          control={<Radio />}
                          label={
                            <div className={styles['avatar-wrapper']}>
                              <UserAvatar
                                className={styles['avatar']}
                                photo={voting.task.customer.photo}
                              />
                              <div className={styles['name']}>{voting.task.customer.fullName}</div>
                            </div>
                          }
                        />
                        <FormControlLabel
                          value="executor"
                          className={styles['radio-label']}
                          control={<Radio />}
                          label={
                            <div className={styles['avatar-wrapper']}>
                              <UserAvatar
                                className={styles['avatar']}
                                photo={voting.task.executor ? voting.task.executor.photo : null}
                              />
                              <div className={styles['name']}>
                                {voting.task.executor ? voting.task.executor.fullName : ''}
                              </div>
                            </div>
                          }
                        />
                      </RadioGroup>
                    </FormControl>
                  ) : (
                    deal && (
                      <div>
                        <div className={styles['value-red']}>
                          {words['user.subcontract.task.confirmationWarning']}
                        </div>
                        <p> </p>
                        <div className={styles['value-red']}>
                          {words['user.subcontract.task.connectMetamaskVariant2']}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>
          {modalData && (
            <VotingModal isShow={isShowModal} onClose={closeModal} comment={modalData} />
          )}
        </>
      )}
    </div>
  )
}

export { VotingDetailsComponent }
