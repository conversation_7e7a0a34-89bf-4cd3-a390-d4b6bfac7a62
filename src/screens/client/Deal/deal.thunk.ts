import { Dispatch } from 'redux'
import { getDeal, setFees } from './deal.actions'
import { EContractAddress } from 'globalConfigs'
import { convertBigIntToInteger } from 'utils/convertBigNumberToInt'
import { IToken } from '../Subcontract/Subcontract.model'
import { getContractService } from 'wagmiContractService'

export const getDealThunk = (taskHash: string) => async (dispatch: Dispatch) => {
  try {
    const { read } = await getContractService(
      EContractAddress.FDEscrowDiamond,
      EContractAddress.FDDealsFacet
    )

    const deal = await read('deals', [taskHash])

    dispatch(getDeal(deal))
    return deal
  } catch (error) {
    console.log('get deal error:', error)
  }
}

export const getFees = (token: IToken) => async (dispatch: Dispatch) => {
  try {
    const { read } = await getContractService(
      EContractAddress.ConfigDeployed,
      EContractAddress.ConfigImplementaniton
    )

    let feeValInt: string = '0'

    const fee = await read('currencyFees', [token.address])
    feeValInt = convertBigIntToInteger(fee, token.decimals)

    if (feeValInt === '0') {
      const defaultFee = await read('defaultCurrencyFee')

      feeValInt = convertBigIntToInteger(defaultFee, 2)
    }

    dispatch(setFees(+feeValInt))
    return fee
  } catch (error) {
    console.error('Error getting fee:', error)
  }
}
