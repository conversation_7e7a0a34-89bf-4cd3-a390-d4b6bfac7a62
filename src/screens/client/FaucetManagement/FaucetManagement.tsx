import React, { FC, useState, useEffect } from 'react'
import { Form } from 'react-final-form'
import FieldFactory from 'components/UiKit/FieldFactory/FieldFactory'
import { Button } from 'components/UiKit/Button'
import CircularLoader from 'components/CircularLoader/CircularLoader'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import { EContractAddress } from 'globalConfigs'
import styles from './FaucetManagement.module.scss'
import { TState } from 'store'
import { useSelector } from 'react-redux'
import { ClientService, getClientService } from 'wagmiClientService'
import { ContractService, getContractService } from 'wagmiContractService'
import { parseUnits } from 'viem'
import { sendTransaction } from '@wagmi/core'
import { wagmiConfig } from 'elements/PrivatRoute/components/wagmiConfig'
import { useAccount } from 'wagmi'

// Whitelist of allowed token addresses
const WHITELISTED_TOKENS: string[] = [
  EContractAddress.TRL_TOKEN,
  EContractAddress.SRCN,
  EContractAddress.NATIVE_TOKEN
  // Add more whitelisted token addresses here
]

interface TokenInfo {
  address: string
  symbol: string
  decimals: number
  dailyAmount: string
}

const FaucetManagement: FC = () => {
  const words = useSelector((state: TState) => state.global.language.words)
  const [client, setClient] = useState<ClientService | null>(null)
  const [account, setAccount] = useState<`0x${string}` | string | undefined>('')
  const [faucetContract, setFaucetContract] = useState<ContractService | null>(null)
  const [filteredTokens, setFilteredTokens] = useState<TokenInfo[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [isPending, setIsPending] = useState<boolean>(false)
  const { address ,isConnected } = useAccount()

  // Initialize Web3 and fetch available tokens
  useEffect(() => {
    const init = async () => {
      try {
        const clientService = await getClientService()
        setClient(clientService)
        console.log('Client service address: ', clientService.address)
        if (!isConnected) {
          toastr(
            EToastrTypeMessage.ERROR,
            words['user.srcnManagement.connectMetamask'] || 'Please connect to MetaMask'
          )
          setIsLoading(false)
          return
        }

        setAccount(address)

        console.log('Setting contract service for faucet...')
        const faucetContractInstance = await getContractService(EContractAddress.FAUCET)
        setFaucetContract(faucetContractInstance)
        console.log('Success')

        // Fetch available tokens from Faucet contract
        const tokensData = await faucetContractInstance.read('getAvailableTokens')

        const [tokenAddresses, dailyAmounts] = [tokensData[0], tokensData[1]]

        // Fetch token details and filter by whitelist
        const tokensInfo: TokenInfo[] = await Promise.all(
          tokenAddresses.map(async (addr: string, index: number) => {
            if (addr === '0x0000000000000000000000000000000000000000') {
              // Native token (tBNB)
              return {
                address: addr,
                symbol: 'tBNB',
                decimals: 18,
                dailyAmount: dailyAmounts[index]
              }
            } else {
              const tokenContract = await getContractService(addr, EContractAddress.USDT)
              const symbol = await tokenContract.read('symbol')
              const decimals = await tokenContract.read('decimals')
              return {
                address: addr,
                symbol,
                decimals: Number(decimals),
                dailyAmount: dailyAmounts[index]
              }
            }
          })
        )

        // Filter tokens based on whitelist
        const filtered = tokensInfo.filter(token => WHITELISTED_TOKENS.includes(token.address))
        setFilteredTokens(filtered)
      } catch (error) {
        console.error('Initialization error:', error)
        toastr(
          EToastrTypeMessage.ERROR,
          words['user.srcnManagement.fetchDataFailed'] || 'Failed to fetch contract data'
        )
      } finally {
        setIsLoading(false)
      }
    }
    init()
  }, [])

  // Handle claim action for a specific token
  const handleClaim = async (tokenAddress: string) => {
    if (!client || !account || isPending || !faucetContract) return

    setIsPending(true)
    try {
      toastr(
        EToastrTypeMessage.INFO,
        words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
      )
      // eslint-disable-next-line prettier/prettier
      await faucetContract.writeAndWait('claim', [tokenAddress], account as `0x${string}`)
    } catch (error) {
      console.error('Claim failed:', error)
      toastr(EToastrTypeMessage.ERROR, words['user.faucetManagment.claimFailed'] || 'Claim failed')
    } finally {
      setIsPending(false)
    }
  }

  // Handle deposit action for a specific token
  const handleDeposit = async (tokenAddress: string, values: any) => {
    const depositAmount = values.depositAmount
    if (!depositAmount || isPending || !client?.signer)  return

    setIsPending(true)
    try {
      const tokenInfo = filteredTokens.find(token => token.address === tokenAddress)
      if (!tokenInfo) return

      const decimals = tokenInfo.decimals
      const depositAmountBN = parseUnits(depositAmount, decimals)


      if (tokenAddress === '0x0000000000000000000000000000000000000000') {
        // Deposit tBNB
         toastr(
              EToastrTypeMessage.INFO,
              words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
         )
        
        // await client?.signer.sendTransaction(
        //   {
        //     to: EContractAddress.FAUCET,
        //     value: depositAmountBN,
        //   }
        // )

        await sendTransaction(wagmiConfig,{
          account: account as `0x${string}`,
          to: EContractAddress.FAUCET,
          value: depositAmountBN,
        })
          
        toastr(
          EToastrTypeMessage.SUCCESS,
          words['user.srcnManagement.depositSuccessful'] || 'Deposit successful'
        )
        
      } else {
        // Deposit ERC-20 token
        const tokenContract = await getContractService(tokenAddress)
        toastr(
          EToastrTypeMessage.INFO,
          words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
        )
       

        // Check allowance
        const allowance = await tokenContract.read('allowance', [account, EContractAddress.FAUCET])
        if (allowance < depositAmountBN) {
          await tokenContract.writeAndWait('approve', [EContractAddress.FAUCET, depositAmountBN], account as `0x${string}`)
        }


        await faucetContract?.writeAndWait('deposit', [tokenAddress, depositAmountBN], account as `0x${string}`)

        toastr(
          EToastrTypeMessage.SUCCESS,
          words['user.srcnManagement.depositSuccessful'] || 'Deposit successful'
        )
      }
    } catch (error) {
      console.error('Deposit failed:', error)
      toastr(
        EToastrTypeMessage.ERROR,
        words['user.srcnManagement.depositFailed'] || 'Deposit failed'
      )
    } finally {
      setIsPending(false)
    }
  }

  if (isLoading) {
    return <CircularLoader />
  }

  const getReadableTokenAmount = (amount: string, decimals: number) => {
    try {
      return Number(amount) / Math.pow(10, decimals)
    } catch (error) {
      return amount
    }
  }

  console.log('filteredTokens', filteredTokens)

  return (
    <div className={styles.container}>
      <h1>{words['user.faucetManagment.title'] || 'Faucet Management'}</h1>
      {isPending && <CircularLoader />}

      {/* Claim Section */}
      <section className={styles.section}>
        <h2>{words['user.faucetManagment.claimTitle'] || 'Claim Tokens'}</h2>
        {filteredTokens.map(token => (
          <div key={token.address} className={styles.tokenRow}>
            <span>
              {token.symbol} - {words['user.faucetManagment.claimLabel'] || 'Daily Claim'}:{' '}
              {getReadableTokenAmount(token.dailyAmount, token.decimals)} {token.symbol}
            </span>
            <Button className={styles.tokenBtn} onClick={() => handleClaim(token.address)}>
              {words['user.faucetManagment.getButton'] || 'Get'}{' '}
              {getReadableTokenAmount(token.dailyAmount, token.decimals)} {token.symbol}
            </Button>
          </div>
        ))}
      </section>

      {/* Deposit Section */}
      <section className={styles.section}>
        <h2>{words['user.faucetManagment.depositTitle'] || 'Deposit Tokens'}</h2>
        <div className={styles['forms-list']}>
          {filteredTokens.map(token => (
            <div key={token.address} className={styles.token}>
              <Form
                onSubmit={values => handleDeposit(token.address, values)}
                render={({ handleSubmit }) => (
                  <form onSubmit={handleSubmit}>
                    <FieldFactory
                      config={[
                        {
                          items: [
                            {
                              name: 'depositAmount',
                              label: (
                                words['user.srcnManagement.depositAmountLabel'] ||
                                'Amount of <TOKEN>> to deposit'
                              ).replace('<TOKEN>', token.symbol),
                              required: true,
                              component: () => ({
                                type: 'input',
                                props: {
                                  variant: 'outlined',
                                  type: 'number'
                                }
                              })
                            }
                          ]
                        }
                      ]}
                      words={{}} // Assuming no internationalization needed; adjust if required
                    />
                    <Button type="submit" disabled={isPending} className={styles.tokenBtn}>
                      {isPending
                        ? 'Processing...'
                        : `${words['user.srcnManagement.depositButton'] || 'Deposit'} ${
                            token.symbol
                          }`}
                    </Button>
                  </form>
                )}
              />
            </div>
          ))}
        </div>
      </section>
    </div>
  )
}

export default FaucetManagement
