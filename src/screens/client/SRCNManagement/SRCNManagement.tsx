import React, { FC, useState, useEffect } from 'react'
import { Form } from 'react-final-form'
import FieldFactory from 'components/UiKit/FieldFactory/FieldFactory'
import { Button } from 'components/UiKit/Button'
import CircularLoader from 'components/CircularLoader/CircularLoader'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import { EContractAddress } from 'globalConfigs'
import styles from './SRCNManagement.module.scss'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { ClientService, getClientService } from 'wagmiClientService'
import { parseUnits } from 'viem'
import { useAccount } from 'wagmi'
import { getContractService } from 'wagmiContractService'

const SRCNManagement: FC = () => {
  const words = useSelector((state: TState) => state.global.language.words)
  const [client, setClient] = useState<ClientService | null>(null)
  // eslint-disable-next-line prettier/prettier
  const [account, setAccount] = useState<`0x${string}` | string | undefined>('')
  const [peggedTokenAddress, setPeggedTokenAddress] = useState<string>('')
  const [peggedTokenDecimals, setPeggedTokenDecimals] = useState<number>(0)
  const [peggedTokenSymbol, setPeggedTokenSymbol] = useState<string>('')
  const [peggedTokenBalance, setPeggedTokenBalance] = useState<bigint | string>('0')
  const [srcnBalance, setSrcnBalance] = useState<bigint | string>('0')
  const [allowance, setAllowance] = useState<bigint | string>('0')
  const [isPending, setIsPending] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const { address, isConnected } = useAccount()

  /** Initialize Web3 and fetch contract data on component mount */
  useEffect(() => {
    const init = async () => {
      try {
        const clientService = await getClientService()
        setClient(clientService)
        // const accounts = await web3Instance.eth.getAccounts()
        if (!isConnected) {
          toastr(
            EToastrTypeMessage.ERROR,
            words['user.srcnManagement.connectMetamask'] || 'Please connect to MetaMask'
          )
          setIsLoading(false)
          return
        }
        // const userAccount = accounts[0]
        setAccount(address)

        const srcnContract = await getContractService(EContractAddress.SRCN)
        const peggedToken = await srcnContract.read('peggedToken', [])
        const decimals = await srcnContract.read('peggedTokenDecimals', [])
        setPeggedTokenAddress(peggedToken)
        setPeggedTokenDecimals(Number(decimals))

        const peggedTokenContract = await getContractService(peggedToken, EContractAddress.USDT)
        const symbol = await peggedTokenContract.read('symbol', [])
        setPeggedTokenSymbol(symbol)

        const peggedBalance = await peggedTokenContract.read('balanceOf', [address])
        setPeggedTokenBalance(peggedBalance)
        const srcnBalanceValue = await srcnContract.read('balanceOf', [address])
        setSrcnBalance(srcnBalanceValue)
        // eslint-disable-next-line prettier/prettier
        const allowanceAmount = await peggedTokenContract.read('allowance', [
          address,
          EContractAddress.SRCN
        ])
        setAllowance(allowanceAmount)
      } catch (error) {
        console.error('Initialization error:', error)
        toastr(
          EToastrTypeMessage.ERROR,
          words['user.srcnManagement.fetchDataFailed'] || 'Failed to fetch contract data'
        )
      } finally {
        setIsLoading(false)
      }
    }
    init()
  }, [])

  /** Refresh balances after transactions */
  const refreshBalances = async () => {
    if (!client || !account) return
    try {
      const peggedTokenContract = await getContractService(
        peggedTokenAddress,
        EContractAddress.USDT
      )
      const srcnContract = await getContractService(EContractAddress.SRCN)

      const peggedBalance = await peggedTokenContract.read('balanceOf', [account])
      setPeggedTokenBalance(peggedBalance)
      const srcnBalanceValue = await srcnContract.read('balanceOf', [account])
      setSrcnBalance(srcnBalanceValue)
    } catch (error) {
      console.error('Error refreshing balances:', error)
    }
  }

  /** Handle deposit action */
  const handleDeposit = async (values: any) => {
    if (!values.depositAmount || isPending) return

    setIsPending(true)
    try {
      const peggedTokenContract = await getContractService(
        peggedTokenAddress,
        EContractAddress.USDT
      )
      const srcnContract = await getContractService(EContractAddress.SRCN)

      // Convert deposit amount to the smallest unit (e.g., wei or token decimals)
      const depositAmount = parseUnits(values.depositAmount, peggedTokenDecimals)

      // Check if the current allowance is sufficient
      if ((allowance as bigint) < depositAmount) {
        try {
          // Allowance is insufficient, request approval
          toastr(
            EToastrTypeMessage.INFO,
            words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
          )

          await peggedTokenContract.writeAndWait('approve', [EContractAddress.SRCN, depositAmount])
          await peggedTokenContract.writeAndWait('approve', [EContractAddress.SRCN, depositAmount])

          toastr(
            EToastrTypeMessage.SUCCESS,
            words['user.srcnManagement.approvalSuccessful'] || 'Approval successful'
          )
        } catch (error) {
          toastr(
            EToastrTypeMessage.ERROR,
            words['user.srcnManagement.transactionFailed'] || 'Transaction failed'
          )
          throw new Error('Approval failed')
        }
      }

      // Proceed with deposit
      try {
        toastr(
          EToastrTypeMessage.INFO,
          words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
        )
        await srcnContract.writeAndWait('deposit', [depositAmount])

        toastr(
          EToastrTypeMessage.SUCCESS,
          words['user.srcnManagement.depositSuccessful'] || 'Deposit successful'
        )

        setAllowance('0')
        await refreshBalances()
      } catch (error) {
        toastr(
          EToastrTypeMessage.ERROR,
          words['user.srcnManagement.transactionFailed'] || 'Transaction failed'
        )
      }
    } catch (error) {
      console.error('Deposit failed:', error)
      toastr(
        EToastrTypeMessage.ERROR,
        words['user.srcnManagement.depositFailed'] || 'Deposit failed'
      )
    } finally {
      setIsPending(false)
    }
  }

  /** Handle withdraw action */
  const handleWithdraw = async (values: any) => {
    if (!values.withdrawAmount || isPending) return

    setIsPending(true)
    try {
      const srcnContract = await getContractService(EContractAddress.SRCN)

      const withdrawAmount = parseUnits(values.withdrawAmount, 8)
      toastr(
        EToastrTypeMessage.INFO,
        words['user.srcnManagement.transactionInProgress'] || 'Transaction in progress'
      )
      await srcnContract.writeAndWait('withdraw', [withdrawAmount])

      toastr(
        EToastrTypeMessage.SUCCESS,
        words['user.srcnManagement.withdrawSuccessful'] || 'Withdraw successful'
      )
      await refreshBalances()
    } catch (error) {
      console.error('Withdraw failed:', error)
      toastr(
        EToastrTypeMessage.ERROR,
        words['user.srcnManagement.withdrawFailed'] || 'Withdraw failed'
      )
    } finally {
      setIsPending(false)
    }
  }

  // Format balances for display
  const peggedTokenBalanceDisplay = (
    Number(peggedTokenBalance) /
    10 ** peggedTokenDecimals
  ).toFixed(4)
  const srcnBalanceDisplay = (Number(srcnBalance) / 10 ** 8).toFixed(4)

  if (isLoading) {
    return <CircularLoader />
  }

  return (
    <div className={styles.container}>
      <h1>{words['user.srcnManagement.title'] || 'SRCN Management'}</h1>
      {isPending && <CircularLoader />}

      {/* Deposit Section */}
      <section className={styles.section}>
        <h2>
          {(
            words['user.srcnManagement.depositHeading'] || 'Deposit <TOKEN>> to receive SRCN'
          ).replace('<TOKEN>', peggedTokenSymbol)}
        </h2>
        <Form
          onSubmit={handleDeposit}
          render={({ handleSubmit }) => (
            <form onSubmit={handleSubmit}>
              <FieldFactory
                config={[
                  {
                    items: [
                      {
                        name: 'depositAmount',
                        label: (
                          words['user.srcnManagement.depositAmountLabel'] ||
                          'Amount of <TOKEN> to deposit'
                        ).replace('<TOKEN>', peggedTokenSymbol),
                        required: true,
                        component: () => ({
                          type: 'input',
                          props: {
                            variant: 'outlined',
                            type: 'number'
                          }
                        })
                      }
                    ]
                  }
                ]}
                words={words}
              />
              <Button type="submit" disabled={isPending}>
                {isPending
                  ? 'Processing...'
                  : words['user.srcnManagement.depositButton'] || 'Deposit'}
              </Button>
            </form>
          )}
        />
      </section>

      {/* Withdraw Section */}
      <section className={styles.section}>
        <h2>
          {(
            words['user.srcnManagement.withdrawHeading'] || 'Withdraw SRCN to receive <TOKEN>'
          ).replace('<TOKEN>', peggedTokenSymbol)}
        </h2>
        <Form
          onSubmit={handleWithdraw}
          render={({ handleSubmit }) => (
            <form onSubmit={handleSubmit}>
              <FieldFactory
                config={[
                  {
                    items: [
                      {
                        name: 'withdrawAmount',
                        label:
                          words['user.srcnManagement.withdrawAmountLabel'] ||
                          `Amount of SRCN to withdraw`,
                        required: true,
                        component: () => ({
                          type: 'input',
                          props: {
                            variant: 'outlined',
                            type: 'number'
                          }
                        })
                      }
                    ]
                  }
                ]}
                words={words}
              />
              <Button type="submit" disabled={isPending}>
                {isPending
                  ? 'Processing...'
                  : words['user.srcnManagement.withdrawButton'] || 'Withdraw'}
              </Button>
            </form>
          )}
        />
      </section>

      {/* Balances Display */}
      <div className={styles.balances}>
        <p>
          {(
            words['user.srcnManagement.peggedTokenBalanceLabel'] || 'Your <TOKEN> balance:'
          ).replace('<TOKEN>', peggedTokenSymbol)}{' '}
          {peggedTokenBalanceDisplay}
        </p>
        <p>
          {words['user.srcnManagement.srcnBalanceLabel'] || 'Your SRCN balance:'}{' '}
          {srcnBalanceDisplay}
        </p>
      </div>
    </div>
  )
}

export default SRCNManagement
