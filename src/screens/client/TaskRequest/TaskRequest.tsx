import React, { FC, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { useRouteMatch } from 'react-router'
import { Form, Field } from 'react-final-form'
import moment from 'moment'
import { TState } from 'store'
import toastr from 'utils/toastr'
import { Tooltip } from 'react-tooltip'
import { getTimeAndMaterialsFields } from './TaskRequest.config'
import FieldFactory from 'components/UiKit/FieldFactory/FieldFactory'
import { ETaskType } from 'models'

import { bidRate, config } from 'globalConfigs'
import validation from 'utils/validation'
import { Textarea } from 'components/Textarea'
import { Input } from 'components/UiKit/Inputs'
import { DatePicker } from 'components/UiKit/DatePicker'
import { RedNote } from 'components/RedNote'
import { Button } from 'components/UiKit/Button'
import PurchaseBidsModal from 'components/SubcontractModals/PurchaseBidsModal/PurchaseBidsModal'
import Modal from 'components/Modal'
import { IBudgetData, TTaskRequestProps } from './TaskRequest.model'

import styles from './TaskRequest.module.scss'
import Spinner from 'components/Spinner'
import { isEmpty } from 'utils/lodashReplacements'
import { useAvailableFunds } from 'hooks/useAvailableFunds'
import { EMessageCodes } from 'types/EMessageCodes'

const TaskRequest: FC<TTaskRequestProps> = ({
  taskDataLoading,
  loading,
  error,
  history,
  taskData,
  isTransaction,
  getTaskDataForRequest,
  clearTaskDataForRequest,
  getCandidatForOffer,
  candidat,
  candidatLoading,
  handleMakeProposalThunk,
  isShowSuccessRequestModal,
  setShowSuccessRequestModal,
  pulling
}) => {
  const words = useSelector((state: TState) => state.global.language.words)
  const currentLanguage = useSelector((state: TState) => state.global.language.currentLanguage)
  const isOfferPage = useRouteMatch('/dashboard/subcontract/offer')
  const redirectPath = '/dashboard/subcontract?page=1&task=all-tasks'
  const { userBids, availableProposal } = useAvailableFunds()
  const { taskId, candidatId } = useMemo(() => {
    const item = localStorage.getItem('routes')
    if (item) {
      return JSON.parse(item)
    }
    return { taskId: 0, candidatId: 0 }
  }, [])

  const formHeader = useMemo(() => {
    if (isOfferPage) {
      let title = words['user.subcontract.taskRequest.offerHeader'] || 'Offer <fullName> a task'
      if (title && candidat) {
        title = title.replace('<fullName>', candidat.fullName)
      }
      return title
    }
    return words['user.subcontract.taskRequest.header']
  }, [candidat, words])

  const [modalState, setModalState] = useState(false)

  useEffect(() => console.log('taskData', taskData), [taskData])

  useEffect(() => {
    // Update bids info
    pulling()
    try {
      if (!taskId || typeof taskId !== 'number') throw new Error('taskId is not defined')

      getTaskDataForRequest(taskId)

      if (candidatId) {
        getCandidatForOffer(candidatId, history)
      }
    } catch (_) {
      history.replace(redirectPath)
    }

    return () => {
      localStorage.removeItem('routes')
      clearTaskDataForRequest()
    }
  }, [])

  const closeLoadingModal = () => {
    setShowSuccessRequestModal(false)
    return history.push(redirectPath)
  }

  const getBudgetDataValue = (key: keyof IBudgetData) => {
    if (!taskData || !taskData.budgetData) return ''
    return taskData.budgetData[key] || ''
  }

  const initialValues = useMemo(
    () => ({
      hours: getBudgetDataValue('workingHours'),
      rate: getBudgetDataValue('pricePerHour'),
      isRepeatable: false
    }),
    [getBudgetDataValue]
  )

  const onSubmitHandler = (values: any) => {
    const { hours, rate, budget, deadline, ...rest } = values

    if (userBids < bidRate.makeProposalRate) {
      toastr('error', words[EMessageCodes.YOU_DONT_HAVE_BIDS] || "You don't have enough bids")
    } else {
      const body = {
        ...rest,
        ...(taskData.type === ETaskType.TIME_AND_MATERIALS
          ? { workingHours: +hours, pricePerHour: +rate, budget: +hours * +rate }
          : {}),
        ...(taskData.type !== ETaskType.TIME_AND_MATERIALS
          ? { budget: +budget, deadline: `${moment(deadline).format('YYYY-MM-DD')}T00:00:00.000Z` }
          : {})
      }

      handleMakeProposalThunk(body, taskData, !!isOfferPage, candidat)
    }
  }

  const returnCompletedPage = (children: React.ReactNode) => {
    return (
      <div className={styles.container} style={{ flex: 1 }}>
        <header className={styles.header}>
          <h1>{words['user.header.subcontract']}</h1>
        </header>
        {children}
      </div>
    )
  }

  if (taskDataLoading || candidatLoading) {
    return returnCompletedPage(
      <div className={styles['loader-block']}>
        <Spinner isBackground={false} />
      </div>
    )
  }

  if (isEmpty(taskData)) {
    return returnCompletedPage(
      <div className={styles['message-block']}>
        {words['ERR_NOT_FOUND_TASK_FOR_PROPOSAL'] ||
          'Oh! Something went wrong. There is no such task.'}
      </div>
    )
  }

  if (taskData.alreadySent) {
    return returnCompletedPage(
      <div className={styles['message-block']}>
        {words['ERR_MAKE_PROPOSAL_AGAIN'] ||
          'Oh! You do not have access to this page. You have already send request to this task.'}
      </div>
    )
  }

  if (taskData.isCustomer && !isOfferPage) {
    return returnCompletedPage(
      <div className={styles['message-block']}>
        {words['ERR_MAKE_PROPOSAL_CUSTOMER'] ||
          'Oh! You do not have access to this page. You are the creator of this task.'}
      </div>
    )
  }

  return (
    <>
      {isTransaction && <Spinner isBackground={true} isTransaction={isTransaction} />}
      <div className={styles.container}>
        <header className={styles.header}>
          <h1>{words['user.header.subcontract']}</h1>
        </header>

        <Form initialValues={initialValues} onSubmit={onSubmitHandler}>
          {({ form, handleSubmit }) => {
            const formState = form.getState()

            const checkRequired = () => {
              if (!availableProposal) return
              if (formState.errors && Object.keys(formState.errors).length !== 0) {
                handleSubmit()
                toastr('error', words['user.editProfile.fillRequiredFields'])
              } else {
                handleSubmit()
              }
            }

            return (
              <main className={styles.main}>
                <form onSubmit={handleSubmit} name="form">
                  <section className={styles.form}>
                    <h2>{formHeader}</h2>
                    <div className={styles.rows}>
                      <div className={styles.row}>
                        <div className={styles.message}>
                          <div className={styles.label}>
                            <label>
                              {words['user.subcontract.taskRequest.message']} {<RedNote />}
                            </label>
                          </div>
                          <Field
                            name="comment"
                            validate={validation.required(words['user.requiredMessage'])}
                          >
                            {({ input, meta }) => (
                              <Textarea
                                {...input}
                                isInvalid={meta.error && meta.submitFailed}
                                errorMessage={meta.error}
                              />
                            )}
                          </Field>
                        </div>
                      </div>

                      <div className={styles.row}>
                        {taskData.type === ETaskType.TIME_AND_MATERIALS && (
                          <FieldFactory
                            form={form}
                            config={getTimeAndMaterialsFields(words)}
                            words={words}
                          />
                        )}
                        {taskData.type !== ETaskType.TIME_AND_MATERIALS && (
                          <>
                            <div className={styles.budget}>
                              <div className={styles.label}>
                                <label>
                                  {words['user.subcontract.taskRequest.budget']} {<RedNote />}
                                </label>
                              </div>
                              <Field
                                name="budget"
                                validate={validation.required(words['user.requiredMessage'])}
                              >
                                {({ input, meta }) => (
                                  <Input
                                    {...input}
                                    isInvalid={meta.error && meta.submitFailed}
                                    errorMessage={meta.error}
                                    variant={'outlined'}
                                    type={'number'}
                                  />
                                )}
                              </Field>
                            </div>

                            <div className={styles.term}>
                              <div className={styles.label}>
                                <label>
                                  {words['user.subcontract.taskRequest.term']} {<RedNote />}
                                </label>
                              </div>
                              <Field
                                name="deadline"
                                validate={validation.required(words['user.requiredMessage'])}
                              >
                                {({ input }) => (
                                  //@ts-ignore
                                  <DatePicker
                                    {...input}
                                    locale={currentLanguage}
                                    name={input.name}
                                    onChange={event => {
                                      if (event) {
                                        form.change(input.name, event)
                                      }
                                    }}
                                    autoComplete="off"
                                    dateFormat={config.dateFormat}
                                    selected={input.value ? new Date(input.value) : null}
                                    minDate={new Date()}
                                  />
                                )}
                              </Field>
                            </div>
                          </>
                        )}
                      </div>

                      {taskData.isRepeatable && (
                        <div className={styles.checkboxes}>
                          <Field name={'isRepeatable'} type="checkbox">
                            {({ input }) => (
                              <Input
                                step="1"
                                variant="outlined"
                                type="checkbox"
                                placeholder="hel"
                                {...input}
                              />
                            )}
                          </Field>
                          <label>{words['user.createType.repeatedTask']}</label>
                        </div>
                      )}
                    </div>
                  </section>
                  <section className={styles.buttons}>
                    <div className={styles.bidsButton}>
                      <div className={styles.bidsAmount}>
                        <span>{words['user.subcontract.taskRequest.bids']}: </span>
                        <span>{userBids}</span>
                      </div>
                      <Button size={'md'} color={'green'} onClick={() => setModalState(true)}>
                        <span>{words['user.subcontract.taskRequest.buy']}</span>
                      </Button>
                    </div>
                    <div className={styles.actionsButton}>
                      <Button outline={true} size={'md'} onClick={() => history.goBack()}>
                        <span>{words['user.subcontract.reject']}</span>
                      </Button>
                      <div id="proposalBtnTooltip">
                        <Button
                          onClick={checkRequired}
                          size={'md'}
                          children={words['user.subcontract.continue'] || 'Continue'}
                          disabled={!availableProposal}
                        />
                        <Tooltip
                          className="tooltip"
                          anchorSelect="#proposalBtnTooltip"
                          place="bottom"
                        >
                          {!availableProposal
                            ? words[EMessageCodes.YOU_DONT_HAVE_BIDS] ||
                              "You don't have enough bids"
                            : ''}
                        </Tooltip>
                      </div>
                    </div>
                  </section>
                </form>
              </main>
            )
          }}
        </Form>

        {/* Modals */}
        {modalState && <PurchaseBidsModal onClose={() => setModalState(false)} />}
        <Modal
          isShow={isShowSuccessRequestModal}
          onClose={() => {
            if (!loading) closeLoadingModal()
          }}
          className={styles.LoadingModal}
        >
          {loading && !error && <p>{words['user.subcontract.taskRequest.loading']}</p>}
          {!loading && !error && (
            <div className={styles.success}>
              <p>{words['user.subcontract.taskRequest.success']}</p>
              <div className={styles.btn}>
                <Button onClick={closeLoadingModal}>
                  <span>Ok</span>
                </Button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </>
  )
}

export default TaskRequest
