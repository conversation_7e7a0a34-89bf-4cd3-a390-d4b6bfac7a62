import React, { FC, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { TState } from 'store'
import { TTaskRequestProps } from './TaskPayment.model'
import { Button } from '../../../components/UiKit/Button'
import { ReactComponent as DefaultAvatar } from 'assets/images/default_avatar.svg'
import styles from './TaskPayment.module.scss'
import { activateMetaMask } from '../Web3/web3.thunk'
import toastr, { EToastrTypeMessage } from 'utils/toastr'
import Spinner from 'components/Spinner'
import { ETransactionMessage } from 'components/Spinner/types'
import dayjs from 'utils/dayjs'
import { isEmpty } from 'utils/lodashReplacements'
import { ETaskResponseStatus } from '../Subcontract/components/Response/libs/constants/constants'
import { useAccount } from 'wagmi'

const TaskPayment: FC<TTaskRequestProps> = ({
  history,
  acceptProposalThunk,
  isTransaction,
  currentProposal,
  paymentLoading,
  getProposalThunk,
  clearProposalData,
  transferTokensToContractThunk
}) => {
  const proposalTask = currentProposal ? currentProposal.task : null
  console.log('🚀 ~ task:', proposalTask)
  const words = useSelector((state: TState) => state.global.language.words)

  const { isConnected } = useAccount()

  const handleAcceptProposal = async (): Promise<void> => {
    if (isEmpty(proposalTask)) {
      return toastr('error', 'Task not found')
    }
    if (proposalTask.acceptProposalSignature) {
      return toastr('error', 'Task already accepted')
    }

    try {
      await acceptProposalThunk(proposalTask, currentProposal)
    } catch (err) {
      console.log(err)
    }
  }

  const handleTransferTokens = async () => {
    if (isEmpty(proposalTask)) {
      return toastr('error', 'Task not found')
    }

    try {
      await transferTokensToContractThunk(proposalTask, history)
    } catch (err) {
      toastr(EToastrTypeMessage.ERROR, words[`${ETransactionMessage.TRANSACTION_ERROR}`])
      console.log(err)
    }
  }

  useEffect(() => {
    try {
      const { proposalId } = JSON.parse(localStorage.getItem('routes') || '')
      if (!proposalId || typeof proposalId !== 'number')
        throw new Error('proposalId is not defined')

      getProposalThunk(proposalId)
    } catch (_) {
      history.replace(`/dashboard/subcontract`)
    }

    return () => {
      localStorage.removeItem('routes')
      clearProposalData()
    }
  }, [])

  const returnCompletedPage = (children: React.ReactNode) => {
    return (
      <div className={styles.container} style={{ flex: 1 }}>
        <header className={styles.header}>
          <h1>{words['user.header.subcontract']}</h1>
        </header>
        {children}
      </div>
    )
  }

  if (paymentLoading) {
    return returnCompletedPage(
      <div className={styles['loader-block']}>
        <Spinner isBackground={false} />
      </div>
    )
  }

  if (isEmpty(currentProposal)) {
    return returnCompletedPage(
      <div className={styles['message-block']}>
        Oh! Something went wrong. There is no such proposal.
      </div>
    )
  }

  if (isEmpty(proposalTask)) {
    return returnCompletedPage(
      <div className={styles['message-block']}>
        Oh! Something went wrong. There is no such task.
      </div>
    )
  }

  const { author } = currentProposal
  const position = author.position ? words[author.position.name] : 'Unknown'
  const rank = author.rank ? words[author.rank.name] : 'Unknown'

  return (
    <>
      {isTransaction && <Spinner isBackground={true} isTransaction={isTransaction} />}
      <div className={styles.container}>
        <header className={styles.header}>
          <h1>{words['user.header.taskPayment']}</h1>
        </header>
        <div className={styles.main}>
          <section className={styles.task}>
            <h2>{proposalTask.title}</h2>
            <div className={styles.line} />
            <div className={styles.description}>
              <div className={styles.performer}>
                <span>{words['user.taskPayment.performer']}:</span>
                <div className={styles['author-data']}>
                  <div className={styles['avatar-block']}>
                    {author.photo ? (
                      <img className={styles.avatar} src={author.photo} alt="avatar" />
                    ) : (
                      <DefaultAvatar className={styles.avatar} />
                    )}
                    <p className={styles['reviews']}>
                      <span style={{ color: '#149B58' }}>{author.countPositiveReviews}</span>/
                      <span style={{ color: '#E12F75' }}>{author.countNegativeReviews}</span>
                    </p>
                  </div>
                  <div className={styles['user-info']}>
                    <h2 className={styles['fullname']}>{author.fullName}</h2>
                    <p className={styles['additional-data']}>
                      {rank} / {position}
                    </p>
                  </div>
                </div>
              </div>
              <div className={styles.amount}>
                <span>{words['user.taskPayment.amountPayable']}:</span>
                <div className={styles.total}>
                  <h2>{` ${currentProposal.budget} ${currentProposal.symbol}`}</h2>
                </div>
              </div>
              <div className={styles.term}>
                <span>{words['user.taskPayment.executionPeriod']}:</span>
                <div className={styles.period}>
                  <h2>
                    {words['user.taskPayment.upTo']}
                    {` ${dayjs(currentProposal.deadline).format('DD.MM.YYYY')}`}
                  </h2>
                </div>
              </div>
            </div>
          </section>
          <section className={styles.buttons}>
            <div className={styles.actionsButton}>
              <Button outline={true} size={'lgs'} onClick={() => history.goBack()}>
                <span>{words['back']}</span>
              </Button>
              {!isConnected ? (
                <Button
                  size={'lgs'}
                  children={words['user.taskPayment.activate']}
                  onClick={() => activateMetaMask()}
                />
              ) : (
                <div className={styles.actionButtonsWrapper}>
                  {currentProposal.status !== ETaskResponseStatus.SELECTED ? (
                    <Button
                      size={'exlg'}
                      onClick={handleAcceptProposal}
                      children={words['user.subcontract.voting.acceptProposal']}
                    />
                  ) : (
                    <Button
                      size={'lgs'}
                      onClick={handleTransferTokens}
                      children={words['user.taskPayment.pay']}
                    />
                  )}
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
    </>
  )
}

export default TaskPayment
