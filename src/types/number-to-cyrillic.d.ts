// src/types/number-to-cyrillic.d.ts
declare module 'number-to-cyrillic' {
  /** Options accepted by numberToCyrillic.convert(...) */
  export interface ConvertOptions {
    /** Make the first letter of the result capital */
    capitalize?: boolean

    /**
     * Currency whose name will be attached to the words.
     *  - `'uah'` – гривня
     *  - `'usd'` – долар(и) США
     *  - `'eur'` – євро
     *  - `'rub'` – рубль/рублі/рублів
     *  - `false`  – no currency at all
     */
    currency?: 'uah' | 'usd' | 'eur' | 'rub' | false

    /** Language in which to spell the number */
    language?: 'ua' | 'en' | 'ru'

    /**
     * If `true`, replaces the default English fractional‑unit names
     * (“cent(s)”, “kopeck(s)”) with “kopek”, “kopeks”.
     */
    customDecimalNameCasesForEnglish?: boolean

    /**
     * Optional prefix for the English currency name.
     * Example: `'US '`  ⇒  “US dollars” instead of “dollars”.
     */
    customCurrencyPrefixForEnglish?: string
  }

  /** Structure returned by convert() */
  export interface ConvertResult {
    /** The integer part written out (“сорок п’ять”) */
    convertedInteger: string
    /** Correctly declined name of the integer currency (“доларів США”) */
    integerCurrency: string

    /** The fractional part written out (“двадцять одна”) */
    convertedFractional: string
    /** Declined fractional‑unit name (“копійка/центи/копійок/…”) */
    fractionalCurrency: string

    /** Raw integer part (e.g. 45) */
    integer: number
    /** Raw fractional part (e.g. 21 for 76.21) */
    fractional: number

    /** Currency short name (“грн.”, “USD”, “€”, …) */
    shortName: string
  }

  /** Main function */
  export function convert(amount: number | string, options?: ConvertOptions): ConvertResult

  /** CommonJS style default export (`const ntc = require('number-to-cyrillic')`) */
  const _default: {
    convert: typeof convert
  }
  export = _default
}
