/* eslint-disable no-shadow */
import { TSelectOption } from './components/Select'
import { TWords } from 'elements/SideBar/SideBar.config'
import { IToggleBarData } from 'components/ToggleBar'
import { IChekbox } from 'components/UiKit/Table/components/SetColumns'
import {
  ERestAtBreakevenMode,
  EStopLossMode,
  ETakeProfitMode,
  EOrderType,
  EProfitFixationMode,
  EOrderPrice
} from 'screens/client/CryptoBot/components/ControlPanel/ControlPanel.types'
import { IToggleBarOptions } from 'global.model'
import { ethers } from 'ethers'
import { bsc, bscTestnet } from 'wagmi/chains'

const selectedChain = process.env.REACT_APP_IS_MAINNET === 'true' ? bsc : bscTestnet


export type TConfig = typeof config
export type TMapperNumberString = { [key: number]: string }
export type TMapperStringsNumber = { [key: string]: number }
export type TMultipleSelectOptions = {
  values: number[]
  label: string
}

export const config = {
  trackJsToken: process.env.REACT_APP_TRACK_JS_TOKEN || '476fd1e32d67479e8015c712dc7c41c9',
  trackJsApplication: process.env.REACT_APP_TRACK_JS_APP || 'template',
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  isTest: process.env.NODE_ENV === 'test',
  apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  apiPolygon:
    process.env.REACT_APP_POLYGON_API_URL_SH_ID ||
    // 'http://localhost:8080/http://*************:3001/v1/',
    'https://cors-anywhere.herokuapp.com/http://*************:3001/v1/',
  googleLogin:
    process.env.REACT_APP_GOOGLE_LOGIN ||
    'https://dev-points-system.technorely.com/backend/api/login',
  dateFormat: 'dd.MM.yyyy',
  fileTypeRegExp: /.+\.(\w+)$/,
  fileNameRegExp: /^.+\/\d+_(.+)\./,
  blockPassClientID: process.env.REACT_APP_BLOCKPASS_CLIENT_ID || '',
  socketUrl: process.env.REACT_APP_SOCKET_URL || 'http://localhost:3001',
  authDomain: process.env.REACT_APP_AUTH0_DOMAIN || '',
  authClientId: process.env.REACT_APP_AUTH0_CLIENT_ID || '',
  authAudience: process.env.REACT_APP_AUTH0_AUDIENCE || '',
  authScope: process.env.REACT_APP_AUTH0_SCOPE || '',
  walletConnectProjectId: process.env.REACT_APP_WALLET_CONNECT_PROJECT_ID || '',
  bscscanApikey: process.env.REACT_APP_BSCSCAN_APIKEY || '',
  testingUIisOn: process.env.REACT_APP_TESTING_UI === 'true',
  testingUIPK: process.env.REACT_APP_TESTING_UI_PRIVATE_KEY || '',
  testingUIRPCUrl: process.env.REACT_APP_TESTING_UI_RPC_URL || '',
  buyTokenUrl: process.env.REACT_APP_BUY_TOKEN_URL || '',
  timeSlotsLimit: 5,
  blockchain: {
    // TODO: Replace api key after test
    chain: selectedChain,
    // eslint-disable-next-line prettier/prettier
    urlRPC: process.env.REACT_APP_IS_MAINNET === 'true' ? process.env.REACT_APP_RPC_PROVIDER?.replace('-testnet', '') : (process.env.REACT_APP_RPC_PROVIDER || 'https://data-seed-prebsc-1-s1.binance.org:8545/'),
    urlEtherscanApi: process.env.REACT_APP_IS_MAINNET === 'true' ? process.env.REACT_APP_ETHERSCAN_API?.replace('-testnet','') : process.env.REACT_APP_ETHERSCAN_API
  },
  pancakeSwapTokensUrl:
    process.env.REACT_APP_PANCAKE_SWAP_TOKENS_URL ||
    'https://tokens.pancakeswap.finance/pancakeswap-default.json'
}
export const DATE_FORMAT_FOR_DETAILS = 'DD.MM.YY'
export const address0 = ethers.constants.AddressZero // '******************************************'

export const TYPE_OF_REQUESTS_TIME_OFF: TMapperNumberString = {
  1: 'Panding',
  2: 'Day-off',
  3: 'Absent',
  4: 'Self-paid',
  5: 'Sick',
  6: 'Remote'
}

export const TYPE_OF_CURRENCY: TMapperNumberString = {
  1: 'TRL',
  3: 'EHT20',
  4: 'WAS',
  5: 'XCM',
  6: 'DFX',
  7: 'SRCN'
}

export const STATUS_OF_REQUESTS_TIME_OFF = (words: TWords): TMapperNumberString => {
  return {
    1: words['user.dayOffTracker.table.requestStatus.pending'],
    2: words['user.dayOffTracker.table.requestStatus.approved'],
    3: words['user.dayOffTracker.table.requestStatus.rejected'],
    4: words['user.dayOffTracker.table.requestStatus.canceled'],
    5: words['user.dayOffTracker.table.requestStatus.processed']
  }
}

export const STATUS_OF_REQUESTS_SELECT = (words: TWords): TSelectOption[] => {
  return [
    { value: '1', label: words['user.dayOffTracker.table.requestStatus.pending'] },
    { value: '2', label: words['user.dayOffTracker.table.requestStatus.approved'] },
    { value: '3', label: words['user.dayOffTracker.table.requestStatus.rejected'] }
  ]
}

export const REQUEST_STATUS_COLORS: { [key: number]: string } = {
  1: 'orange',
  2: 'green',
  3: 'red',
  4: 'blue',
  5: 'green'
}
export const TYPE_TRANSACTION = (words: TWords): TMapperNumberString => {
  return {
    1: words['user.profile.table.type.outgoingTransfer'],
    2: words['user.profile.table.type.incomingTransfer']
  }
}

export const ACTIONS_FOR_CHANGE_STATUS_OF_REQUESTS_TIME_OFF: TMapperStringsNumber = {
  approve: 2,
  reject: 3,
  cancel: 4
}

export const FILTER_SELECT_TYPE = (words: TWords): TSelectOption[] => {
  return [
    { value: '@ALL@', label: words['all'] },
    { value: '2', label: words['manager.dayOffTracker.details.requestType.dayOff'] },
    { value: '3', label: words['manager.dayOffTracker.details.requestType.absent'] },
    { value: '5', label: words['manager.dayOffTracker.details.requestType.sick'] },
    { value: '6', label: words['manager.dayOffTracker.details.requestType.remote'] },
    { value: '4', label: words['manager.dayOffTracker.details.requestType.selfPaid'] }
  ]
}

export const FILTER_SELECT_STATUS = (words: TWords): TSelectOption[] => {
  return [
    { value: '@ALL@', label: words['all'] },
    { value: '1', label: words['user.dayOffTracker.table.requestStatus.pending'] },
    { value: '2', label: words['user.dayOffTracker.table.requestStatus.approved'] },
    { value: '3', label: words['user.dayOffTracker.table.requestStatus.rejected'] },
    { value: '4', label: words['user.dayOffTracker.table.requestStatus.canceled'] },
    { value: '5', label: words['user.dayOffTracker.table.requestStatus.processed'] }
  ]
}

export const FILTER_SELECT_ACCAUNT_STATUS = (words: TWords): TSelectOption[] => {
  return [
    { value: '@ALL@', label: words['all'] },
    { value: '1', label: words['admin.table.filter.activated'] },
    { value: '2', label: words['admin.table.filter.deactivated'] }
  ]
}

export const FILTER_SELECT_STATUS_FOR_USER = (words: TWords): TSelectOption[] => {
  return [
    { value: '@ALL@', label: words['all'] },
    { value: '1', label: words['user.dayOffTracker.table.requestStatus.pending'] },
    { value: '2', label: words['user.dayOffTracker.table.requestStatus.approved'] },
    { value: '3', label: words['user.dayOffTracker.table.requestStatus.rejected'] }
  ]
}

export const FILTER_SELECT_INITIATIVES_STATUS = (words: TWords): TSelectOption[] => {
  return [
    { value: '@ALL@', label: words['all'] },
    { value: '1', label: words['user.dayOffTracker.table.requestStatus.pending'] },
    { value: '5', label: words['user.dayOffTracker.table.requestStatus.processed'] }
  ]
}

export const LIFE_PRIORITIES = (words: TWords): TSelectOption[] => {
  return [
    { value: '1', label: words['user.profile.mainInfo.livePriorities.family'] },
    { value: '2', label: words['user.profile.mainInfo.livePriorities.career'] },
    { value: '3', label: words['user.profile.mainInfo.livePriorities.selfDev'] },
    { value: '4', label: words['user.profile.mainInfo.livePriorities.spiritDev'] },
    { value: '5', label: words['user.profile.mainInfo.livePriorities.finances'] },
    { value: '6', label: words['user.profile.mainInfo.livePriorities.charity'] },
    { value: '7', label: words['user.profile.mainInfo.livePriorities.phisicalImprovement'] }
  ]
}

export const AMOUNT_OF_CHILDREN = (words: TWords): TSelectOption[] => {
  return [
    { value: '0', label: words['user.editProfile.notSelected'] },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' },
    { value: '6', label: '6' },
    { value: '7', label: '7' },
    { value: '8', label: '8' },
    { value: '9', label: '9' },
    { value: '10', label: '10' }
  ]
}

export const AMOUNT_OF_LEVEL = (words: TWords): TSelectOption[] => {
  return [
    { value: '0', label: words['user.editProfile.notSelected'] },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' },
    { value: '6', label: '6' },
    { value: '7', label: '7' },
    { value: '8', label: '8' },
    { value: '9', label: '9' },
    { value: '10', label: '10' }
  ]
}

export const TYPES_TRANSACTIONS = (words: TWords): TSelectOption[] => {
  return [
    { value: '1', label: words['user.profile.table.type.outgoingTransfer'] },
    { value: '2', label: words['user.profile.table.type.incomingTransfer'] }
  ]
}

export const FILTER_SELECT_ROLES_FOR_USERS = (words: TWords): TSelectOption[] => {
  return [
    { value: '2', label: words['roles.admin'] },
    { value: '3', label: words['roles.manager'] },
    { value: '4', label: words['roles.user'] },
    { value: '5', label: words['roles.ledgenManager'] },
    { value: '6', label: words['roles.teamMember'] }
  ]
}

export const ROLES = (words: TWords): TMapperNumberString => {
  return {
    1: words['roles.superAdmin'],
    2: words['roles.admin'],
    3: words['roles.manager'],
    4: words['roles.user'],
    5: words['roles.ledgenManager'],
    6: words['roles.teamMember']
  }
}

export const PRODUCT_CALENDAR_TYPE = (words: TWords): TMapperNumberString => {
  return {
    1: words['admin.productionCalendar.new.type.workingDay'],
    2: words['admin.productionCalendar.new.type.holiday']
  }
}

export const FILTER_SELECT_PRODUCT_CALENDAR_TYPE = (words: TWords): TSelectOption[] => {
  return [
    { value: '1', label: words['admin.productionCalendar.new.type.workingDay'] },
    { value: '2', label: words['admin.productionCalendar.new.type.holiday'] }
  ]
}

export enum EPageLimits {
  USERS = 200,
  MANAGERS = 200,
  GOODS = 20,
  ACHIEVEMENTS = 20,
  HISTORY = 10,
  TRANSACTION = 10,
  TEAM = 20,
  PROJECTS = 16
}

export enum EVerificationPolygon {
  FALSE = 'false',
  DATA = 'data',
  IN_PROGRESS = 'inprogress',
  CLAIMS = 'claims',
  TRUE = 'true'
}

export enum EVerificationIdenfy {
  FALSE = 'false',
  IN_PROGRESS = 'inprogress',
  CLAIMS = 'claims',
  TRUE = 'true'
}

export enum EVerificationBlockpass {
  NOT_EXIST = 'not_exist',
  IN_COMPLETE = 'incomplete',
  WAITING = 'waiting',
  IN_REVIEW = 'inreview',
  REVIEW_REQUESTED = 'review_requested',
  REJECTED = 'rejected',
  BLOCKED = 'blocked',
  APPROVED = 'approved'
}

export const exchangeSettingsLimits = {
  allocationPercent: { min: 0.001, max: 5 },
  fundingRateLimit: { min: 0.001, max: 1 },
  orderPrice: { min: 0.25, max: 1000 },
  initialBalance: { min: 1, max: 10000000 },
  maxNumPositions: { min: 1, max: 200 },
  minSumForHoldOrder: { min: 11, max: 1000 },
  percentForHold: { min: 0.001, max: 100 },
  percentageForStability: { min: 1, max: 99 },
  maxOrderPrice: { min: 1, max: 2000 },
  takeProfit: { min: 1, max: 100 },
  trailing: { min: 0.1, max: 1e4 },
  trailingTrigger: { min: 0.01, max: 1e4 },
  limitTokenAlive: { min: 0, max: 200 }
}

export const channelsMinMax = {
  autoAveragingTP: { min: 5, max: 2000 },
  closeWhenLessPercent: { min: 1, max: 99 },
  firstStepAveraging: { min: 100, max: 2000 },
  maxAutoAveraging: { min: 0, max: 5 },
  maxChannelAveraging: { min: 0, max: 5 },
  numberOfTakes: { min: 1, max: 7 },
  onlyMoveTPStep: { min: 0, max: 100 },
  otherStepsAveraging: { min: 100, max: 2000 },
  percentagePosToAver: { min: 0, max: 300 },
  periodAfterOMTPStep: { min: 0, max: 90 },
  secondStepAveraging: { min: 100, max: 2000 },
  stopLoss: { min: 5, max: 2000 },
  takeProfit: { min: 5, max: 2000 }
}

export const profitFixationModeSelectOptions = (words: TWords) => [
  { label: words['crypto-bot.standard'], value: EProfitFixationMode.ORDER },
  { label: words['crypto-bot.fixedProfitOnBalance'], value: EProfitFixationMode.TOTAL_BALANCE },
  { label: words['crypto-bot.trail'], value: EProfitFixationMode.TOTAL_BALANCE_TRAILING }
]

export const orderPriceModeSelectOptions = (words: TWords) => [
  { label: words['crypto-bot.betInUSDT'], value: EOrderPrice.FIXED },
  { label: words['crypto-bot.betPercentageOfDeposit'], value: EOrderPrice.ALLOCATION }
]

export const restAtBreakevenModeSelectOptions = [
  { label: 'WITHOUT', value: ERestAtBreakevenMode.WITHOUT },
  { label: 'ALWAYS', value: ERestAtBreakevenMode.ALWAYS }
]

export const stopLossModeModeSelectOptions = [
  { label: 'WITHOUT', value: EStopLossMode.WITHOUT },
  { label: 'MANUAL', value: EStopLossMode.MANUAL },
  { label: 'SIGNAL', value: EStopLossMode.SIGNAL }
]

export const takeProfitModeSelectOptions = [
  { label: 'MANUAL', value: ETakeProfitMode.MANUAL },
  { label: 'WITHOUT', value: ETakeProfitMode.WITHOUT },
  { label: 'SIGNAL', value: ETakeProfitMode.SIGNAL },
  { label: 'MANUAL_MULTIPLE', value: ETakeProfitMode.MANUAL_MULTIPLE }
]

export const openOrderTypeSelectOptions = [
  { label: 'LIMIT', value: EOrderType.LIMIT },
  { label: 'MARKET', value: EOrderType.MARKET }
]

export const closeOrderTypeSelectOptions = [
  { label: 'LIMIT', value: EOrderType.LIMIT },
  { label: 'MARKET', value: EOrderType.MARKET }
]

export const PERIOD_OF_POLLING: number = 30000
export const PAGING_LIMIT: number = 100

export enum EPointsEdit {
  STEP = 100
}

export enum EPointsSettings {
  STEP_FEE_CHARGER = 1,
  STEP_POINTS_FUND = 1000,
  STEP_ACHIEVEMENTS = 1000
}

export enum ERowSize {
  DEFAULT = 50,
  SMALL = 56,
  MIDDLE = 72,
  LARGE = 120
}

export enum EProfileHistory {
  MAIN_INFO = 'main-info',
  CAREER = 'career',
  RESUME = 'resume',
  TIME_OFF_BALANCE = 'time-off-balance',
  POINTS_SYSTEM = 'points-system',
  FEEDBACKS = 'feedbacks',
  VERIFICATION = 'verification',
  ENTREPRENEUR_DATA = 'entrepreneur-data'
}

export enum ESubcontractHistory {
  ALL_TASKS = 'all-tasks',
  MY_TASK = 'my-tasks',
  MY_ANSWERS = 'my-answers',
  PERFORMERS = 'performers',
  PLANNING = 'planning',
  DRAFTS = 'drafts',
  INVOICES = 'invoices'
}

export enum ECryptoBotHistory {
  GENERAL = 'general',
  CHART = 'chart',
  POSITIONS = 'positions'
}

export const enum EAdvancedSettingsHistory {
  EXCHANGE = 'exchange',
  CHANNELS = 'channels'
}

export enum ESubcontractAdminHistory {
  ALL_TASKS_ADMIN = 'all-tasks-admin',
  DEACTIVATED = 'deactivated'
}

export enum EVacanciesAdminHistory {
  ALL_VACANCIES_ADMIN = 'all-vacancies-admin',
  ARCHIVED = 'archived'
}

export enum EVerificationAdminHistory {
  ALL_VERIFY = 'all-verify',
  NOT_VERIFY = 'not-verify',
  ACCEPTED_VERIFY = 'accepted-verify'
}

export enum EResponsesHistory {
  ALL_RESPONSES = 'all-responses',
  MY_PERFORMER = 'my-performer',
  CALLS = 'calls',
  AGREED = 'agreed',
  COMMUNICATION = 'communication'
}

export const enum EContractAddress {
  // FIX_DEAL_ESCROW = '******************************************',
  // TRL_TOKEN = '******************************************',
  // SRCN = '******************************************',
  // VOTING = '0x1105784E359A8A2cfB430a34b3BEBda6004d77C6',
  USDT = '0x337610d27c682e347c9cd60bd4b3b107c9d34ddd',
  FAUCET = '0x72086580B99b222CAB38418302A4aD6371f809d0',

  // *** NEW ****
  NATIVE_TOKEN = '******************************************',
  TRL_TOKEN = '0x27e55C3ECC216CA0EfEE6f1C24a8ecE63eE13525',
  SRCN = '0xa1eb7d20d261EF64898254a01Fe8360270ec9DC1',
  FDEscrowDiamond = '0xCa5a9050B48E21CfaCE0D8e531986997879bCe9d',
  FDTaskFacet = '0xbBddD352fF4c2259CEdEf368065583E4808565B9',
  FDFundsFacet = '0x5798BCF54EFDFc338F93b36B1C0F455D9102BE1e',
  FDDealsFacet = '0x725882B396f3F84212DceA6Eff0D4c1ca48D31Ac',
  FDVotingFacet = '0x962edd744Ae93d735404ce5a1bCb6C40558dc5c0',
  FDBidFacet = '0x92aDE2Be346edb4976e8c69263810316AFf70E5A',
  ConfigDeployed = '0x5Dff3B6Db94C95B1E57CeD160F8EA72371A2467C',
  ConfigImplementaniton = '0xeBAc3501534cF65117339306b2fCC6D8E3af763d'
}

export enum EProjectHistory {
  CURRENT_TEAM = 'current-team',
  VACANCIES = 'vacancies',
  INVOICES = 'invoices',
  TEAM_ARCHIVE = 'team-archive'
}

export enum EEntrepreneurAdminHistory {
  LIST_ENTREPRENEURS = 'list',
  FORMULATED_INVOICES = 'invoices'
}

export enum ETransactionHistory {
  GOODS = 'goods',
  ACHIEVEMENTS = 'initiative',
  TRANSACTIONS = 'transactions'
}

export enum EFeedbackOptions {
  ALL = 'all',
  POSITIVE = 'positive',
  NEGATIVE = 'negative'
}

export enum ETaskCancellationOptions {
  WAITING = 'waiting',
  CANCELLED = 'cancelled'
}

export enum EFaq {
  PROCESSES = 'processes',
  WEEKENDS = 'weekends',
  POINTSSYSTEM = 'pointssystem'
}

export const enum EAdminSettings {
  TASK_CANCELLATION = 'taskCancellation',
  CALENDAR = 'calendar',
  POINTS_SYSTEM = 'pointsSystem',
  SUBCONTRACT = 'subcontract',
  ENTREPRENEUR_SERVICES = 'entrepreneur-services',
  BANKS = 'banks',
  CURRENCIES = 'currencies',
  COMPANY_BANK_ACCOUNTS = 'company-bank-accounts',
  COMPANY_DATA = 'company-data'
}

export const USER_ROLE = 'userRole'
export const ADMIN_ROLE = 'adminRole'

export const requiredOptions: any = {
  'main-info': ['rank', 'position', 'phoneNumber', 'hometown', 'city', 'dateOfBirth'],
  career: ['technologies', 'careerGoal']
}

export const getTransactionToggleBarOptions = (words: TWords): IToggleBarData[] => [
  {
    value: ETransactionHistory.GOODS,
    title: words['user.profile.goodsService']
  },
  {
    value: ETransactionHistory.ACHIEVEMENTS,
    title: words['user.profile.initiatives']
  },
  {
    value: ETransactionHistory.TRANSACTIONS,
    title: words['user.profile.transactions']
  }
]

export const getFeedbacksToggleBarOptions = (words: TWords): IToggleBarData[] => [
  {
    value: EFeedbackOptions.ALL,
    title: words['user.profile.feedbacks.all']
  },
  {
    value: EFeedbackOptions.POSITIVE,
    title: words['user.profile.feedbacks.positives']
  },
  {
    value: EFeedbackOptions.NEGATIVE,
    title: words['user.profile.feedbacks.negatives']
  }
]

export const getTaskCancellationToggleBarOptions = (words: TWords): IToggleBarData[] => [
  {
    value: ETaskCancellationOptions.WAITING,
    title: words['admin.settings.subcontract.pending']
  },
  {
    value: ETaskCancellationOptions.CANCELLED,
    title: words['admin.settings.subcontract.cancelled']
  }
]

export const getToggleBarOptions = (
  words: TWords,
  isAdmin: boolean,
  isContractor: boolean,
  isMyProfile: boolean
): IToggleBarOptions[] => {
  const toggleBarOptions = []

  if (isContractor) {
    toggleBarOptions.push(
      {
        value: EProfileHistory.RESUME,
        title: words['user.profile.togglebar.resume'] || 'Resume'
      }
    )

    return toggleBarOptions
  }

  toggleBarOptions.push({
      value: EProfileHistory.MAIN_INFO,
      title: words['user.profile.togglebar.mainInfo']
    },
    {
      value: EProfileHistory.CAREER,
      title: words['user.profile.togglebar.career']
    },
    {
      value: EProfileHistory.RESUME,
      title: words['user.profile.togglebar.resume'] || 'Resume'
    })

  if (isAdmin || isMyProfile) {
    toggleBarOptions.push(
      {
        value: EProfileHistory.ENTREPRENEUR_DATA,
        title: words['user.profile.togglebar.entrepreneurData'] || 'Full Entrepreneur Data'
      },
      {
        value: EProfileHistory.TIME_OFF_BALANCE,
        title: words['user.profile.togglebar.daysOff']
      },
      {
        value: EProfileHistory.POINTS_SYSTEM,
        title: words['user.profile.togglebar.pointsSystem']
      }
    )
  }

  return toggleBarOptions
}


export const getEditProfileToggleBarOptions = (
  words: TWords,
  isAdmin: boolean,
  isContractor: boolean
): IToggleBarOptions[] => {
  const toggleBarOptions = [
    {
      value: EProfileHistory.MAIN_INFO,
      title: words['user.profile.togglebar.mainInfo']
    }
  ]
  if (isAdmin && !isContractor) {
    toggleBarOptions.push(
    {
      value: EProfileHistory.CAREER,
      title: words['user.profile.togglebar.career']
    })
  }

  toggleBarOptions.push(
    {
      value: EProfileHistory.RESUME,
      title: words['user.profile.togglebar.resume'] || 'Resume'
    })

  if(isContractor) {
    return toggleBarOptions
  }

  if (isAdmin) {
    toggleBarOptions.push(
      {
        value: EProfileHistory.ENTREPRENEUR_DATA,
        title: words['user.profile.togglebar.entrepreneurData'] || 'Full Entrepreneur Data'
      },
      {
        value: EProfileHistory.TIME_OFF_BALANCE,
        title: words['user.profile.togglebar.daysOff']
      },
      {
        value: EProfileHistory.POINTS_SYSTEM,
        title: words['user.profile.togglebar.pointsSystem']
      }
    )
  }

  return toggleBarOptions
}

export const getToggleBarOptionsForSubcontract = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: ESubcontractHistory.ALL_TASKS,
      title: words['user.subcontract.nav.allTasks'],
      tip: words['user.tooltip.subcontract.allTask'] || 'List of all active tasks.'
    },
    {
      value: ESubcontractHistory.MY_TASK,
      title: words['user.subcontract.nav.myTasks'],
      tip:
        words['user.tooltip.subcontract.myTask'] ||
        'A list of tasks I`ve created, including cancelled ones, but no drafts.'
    },
    {
      value: ESubcontractHistory.MY_ANSWERS,
      title: words['user.subcontract.nav.myResponses'],
      tip:
        words['user.tooltip.subcontract.myResponse'] ||
        'List of tasks to which I have submitted a response as an executor.'
    },
    // {
    //   value: ESubcontractHistory.PLANNING,
    //   title: words['user.subcontract.nav.planning']
    // },
    {
      value: ESubcontractHistory.DRAFTS,
      title: words['user.subcontract.nav.drafts'],
      tip:
        words['user.tooltip.subcontract.myDrafts'] ||
        'My drafts list. Tasks created but not published. Only I can see them.'
    }
  ]
  return toggleBarOptions
}

export const getToggleBarOptionsForCrypto = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: ECryptoBotHistory.GENERAL,
      title: words['crypto-bot.generalInfo']
    },
    {
      value: ECryptoBotHistory.CHART,
      title: words['crypto-bot.profitLossChart']
    },
    {
      value: ECryptoBotHistory.POSITIONS,
      title: words['crypto-bot.openPositions']
    }
  ]
  return toggleBarOptions
}

export const getToggleBarOptionsForAdvancedBotSettings = (): IToggleBarData[] => {
  return [
    {
      value: EAdvancedSettingsHistory.EXCHANGE,
      title: 'Биржи'
    },
    {
      value: EAdvancedSettingsHistory.CHANNELS,
      title: 'Каналы'
    }
  ]
}

export const getToggleBarOptionsForSubcontractTasks = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: ESubcontractAdminHistory.ALL_TASKS_ADMIN,
      title: words['user.subcontract.nav.allTasks']
    },
    {
      value: ESubcontractAdminHistory.DEACTIVATED,
      title: words['user.subcontract.nav.deactivated']
    }
  ]
  return toggleBarOptions
}

export const getToggleBarOptionsForEntrepreneurs = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: EEntrepreneurAdminHistory.LIST_ENTREPRENEURS,
      title: words['entrepreneur.list']
    },
    {
      value: EEntrepreneurAdminHistory.FORMULATED_INVOICES,
      title: words['entrepreneur.invoices']
    }
  ]
  return toggleBarOptions
}
// add localization*
export const getToggleBarOptionsForVacancies = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: EVacanciesAdminHistory.ALL_VACANCIES_ADMIN,
      title: words['vacancies.list']
    },
    {
      value: EVacanciesAdminHistory.ARCHIVED,
      title: words['vacancies.archive']
    }
  ]
  return toggleBarOptions
}

export const getToggleBarOptionsForVerifications = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: EVerificationAdminHistory.ALL_VERIFY,
      title: words['user.subcontract.nav.allTasks']
    },
    {
      value: EVerificationAdminHistory.NOT_VERIFY,
      title: 'Not Verify'
    },
    {
      value: EVerificationAdminHistory.ACCEPTED_VERIFY,
      title: 'Accepted Verify'
    }
  ]
  return toggleBarOptions
}

export const getToggleBarOptionsForSubcontractResponse = (
  words: TWords,
  isCustomer: boolean,
  isCommunication: boolean
): IToggleBarData[] => {
  let toggleBarOptions = [
    {
      value: EResponsesHistory.CALLS,
      title: words['user.subcontract.calls'] || 'Calls' // words['user.subcontract.myTask.nav.interview']
    }
  ]
  if (isCustomer) {
    toggleBarOptions = [
      {
        value: EResponsesHistory.ALL_RESPONSES,
        title: words['user.subcontract.myTask.nav.allRes']
      },
      ...toggleBarOptions,
      {
        value: EResponsesHistory.AGREED,
        title: words['user.subcontract.myTask.nav.agreed']
      },
      {
        value: EResponsesHistory.MY_PERFORMER,
        title: words['user.subcontract.myTask.nav.selectPerformer']
      }
    ]
  }
  if (isCommunication) {
    toggleBarOptions.push({
      value: EResponsesHistory.COMMUNICATION,
      title: words['user.subcontract.communication'] || 'Communication'
    })
  }
  return toggleBarOptions
}

export const getToggleBarOptionsForProject = (words: TWords): IToggleBarData[] => {
  const toggleBarOptions = [
    {
      value: EProjectHistory.CURRENT_TEAM,
      title: words['project.team.current']
    },
    {
      value: EProjectHistory.VACANCIES,
      title: words['project.team.vancies']
    },
    {
      value: EProjectHistory.INVOICES,
      title: words['project.team.invoices']
    },
    {
      value: EProjectHistory.TEAM_ARCHIVE,
      title: words['project.team.archive']
    }
  ]
  return toggleBarOptions
}

export const setToggleBarOptionsTeam = (words: TWords): IToggleBarData[] => {
  return [
    {
      value: 'active',
      title: words['admin.team.toggle.active']
    },
    {
      value: 'deleted',
      title: words['admin.team.toggle.deactivated']
    }
  ]
}

export const checkboxConfig = (words: TWords, deleted: string): IChekbox[] => {
  const checkboxConfigArray = [
    { name: 'fullName', label: words['admin.users.table.filters.userName'], value: true },
    { name: 'position', label: words['user.profile.career.position'], value: true },
    { name: 'rank', label: words['user.profile.career.level'], value: true },
    { name: 'city', label: words['user.profile.mainInfo.location'], value: true },
    {
      name: 'department',
      label: words['user.profile.career.department'],
      value: false
    },
    {
      name: 'typeOfEmployment',
      label: words['user.profile.career.occupation'],
      value: true
    },
    { name: 'workingHoursPerDay', label: words['user.profile.career.workingHours'], value: false },
    { name: 'firstWorkingDay', label: words['user.profile.career.hiringDate'], value: true },
    {
      name: 'probationEndDate',
      label: words['user.profile.career.probationEndDate'],
      value: false
    },
    { name: 'salary', label: words['user.profile.career.salary'], value: true },
    { name: 'dateOfBirth', label: words['user.profile.mainInfo.dob'], value: false },
    { name: 'maritalStatus', label: words['user.profile.mainInfo.martialStatus'], value: false },
    {
      name: 'numberOfChildren',
      label: words['user.profile.mainInfo.numberOfChildren'],
      value: false
    },
  ]

  if (deleted === 'true') {
    checkboxConfigArray.splice(
      4,
      0,
      { name: 'terminationDate', label: words['admin.team.terminationDate'], value: true },
      { name: 'terminationInitiator', label: words['admin.team.terminationInitiator'], value: true }
    )
  }

  return checkboxConfigArray
}

export enum EPolicy {
  TERMS_OF_SERVICE = 'terms-of-service',
  PRIVACY_POLICY = 'privacy-policy',
  COOKIE_POLICY = 'cookie-policy'
}

export enum EAcceptPolicyType {
  ACCEPT = 'accept',
  DECLINE = 'decline'
}

export enum ERequestStatus {
  PENDING = 1,
  APPROVED = 2,
  REJECTED = 3,
  CANCELED = 4,
  PROCESSED = 5
}

export const bidRate = {
  makeProposalRate: 1, // 1 proposal = 1 bid
  callsRate: 2 // 1 call = 2 bids
}

export const retrySettings = {
  maxRetries: 3,
  seconds: 1
}
